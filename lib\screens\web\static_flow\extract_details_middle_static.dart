import 'package:flutter/material.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/providers/object_creation_provider.dart';
import 'package:nsl/models/object_creation_model.dart';
import 'package:nsl/screens/web/static_flow/components/manual_processing_component.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';
import 'dart:math' as math;
import 'package:provider/provider.dart';

/// Helper class for column width constraints
class ColumnConstraints {
  final double minWidth;
  final double maxWidth;

  const ColumnConstraints({
    required this.minWidth,
    required this.maxWidth,
  });
}

class ExtractDetailsMiddleStatic extends StatefulWidget {
  final String? sessionId; // New session-based API support
  final String? userIntent;

  const ExtractDetailsMiddleStatic({
    super.key,
    this.sessionId,
    this.userIntent,
  });

  @override
  State<ExtractDetailsMiddleStatic> createState() =>
      _ExtractDetailsMiddleStaticState();
}

class _ExtractDetailsMiddleStaticState
    extends State<ExtractDetailsMiddleStatic> {
  late AccordionController _accordionController;

  // Simple scroll controller without complex synchronization
  final ScrollController _scrollController = ScrollController();

  // Data storage for tables - initialized as empty, no hardcoded fallback dataEnumValue
  List<ObjectAttribute> _attributeData = [];
  List<EntityRelationship> _relationshipData = [];
  List<BusinessRule> _businessRuleData = [];
  List<EnumValue> _enumValueData = [];
  List<SecurityClassification> _securityClassificationData = [];
  List<SystemPermission> _systemPermissionData = [];



  Map<String, List<Map<String, String>>> _tableData = {};

  // Control attribute list visibility in dialog
  bool _showAttributeList = false;
  bool _showRelationshipList = false;
  bool _showBusinessRuleList = false;
  bool _showEnumValuesList = false;
  bool _showSystemPermissionsList = false;
  bool _showSecurityClassificationList = false;

  @override
  void initState() {
    super.initState();
    _accordionController = AccordionController();
    _accordionController.addListener(() {
      setState(() {});
    });

    // No complex synchronization needed

    // Load API data on initialization
    _loadApiData();
  }

  // Removed old scroll synchronization methods

  /// Get default user intent for testing if none provided
  // String get _defaultUserIntent =>
  //     "I need to build a comprehensive CRM system. I need Contact entities with personal information like names, email addresses, phone numbers, job titles, and company affiliations. Each Contact can have multiple communication preferences, social media profiles, and interaction history. Contacts will have lead sources, qualification status, and engagement scoring.";

  /// Load data from the API using provider
  Future<void> _loadApiData() async {
    final objectCreationProvider =
        Provider.of<ObjectCreationProvider>(context, listen: false);

    bool success = false;

    try {
      // Check if we have a userIntent to run the complete workflow
      if (widget.userIntent != null && widget.userIntent!.isNotEmpty) {
        // Use the complete workflow: create job, wait for completion, then fetch entities
        success =
            await objectCreationProvider.executeCompleteExtractionWorkflow(
          userIntent: widget.userIntent!,
        );
      }
      // Check if we have a sessionId to fetch entities directly (new API)
      else if (widget.sessionId != null && widget.sessionId!.isNotEmpty) {
        // Use direct entity fetching for existing session
        success =
            await objectCreationProvider.getSessionEntities(widget.sessionId!);
      }
      // No sessionId or userIntent provided - use default behavior
      // else {
      // Use the complete workflow with default user intent
      // success =
      //     await objectCreationProvider.executeCompleteExtractionWorkflow(
      //   userIntent: _defaultUserIntent,
      // );
      // }

      if (success) {
        print(
            'Successfully loaded ${objectCreationProvider.objects.length} objects from API');
        print('Current session ID: ${objectCreationProvider.currentSessionId}');

        // Log details about loaded entities for debugging
        for (int i = 0; i < objectCreationProvider.objects.length; i++) {
          final entity = objectCreationProvider.objects[i];
          print(
              'Entity $i: ${entity.name} (${entity.displayName}) - Type: ${entity.type}');
        }
      } else {
        print('API Error: ${objectCreationProvider.error}');
        print(
            'Loading states - Extraction: ${objectCreationProvider.isLoadingExtraction}, JobEntities: ${objectCreationProvider.isLoadingJobEntities}');
      }
    } catch (e) {
      print('Exception in _loadApiData: $e');
      print('Stack trace: ${StackTrace.current}');
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _accordionController.dispose();
    super.dispose();
  }

  /// Helper method to get status text based on count
  String _getStatusFromCount(int count) {
    if (count == 0) {
      return 'Missing';
    } else if (count > 0 && count < 5) {
      return 'Partial Completion';
    } else {
      return 'Completed';
    }
  }

  /// Helper method to get background color based on count
  Color _getBackgroundColorFromCount(int count) {
    if (count == 0) {
      return const Color(0xFFFEE2E2); // Red background for missing
    } else if (count > 0 && count < 5) {
      return const Color(0xFFFEF3C7); // Yellow background for partial
    } else {
      return const Color(0xFFD1FAE5); // Green background for completed
    }
  }

  /// Helper method to get text color based on count
  Color _getTextColorFromCount(int count) {
    if (count == 0) {
      return const Color(0xFF991B1B); // Red text for missing
    } else if (count > 0 && count < 5) {
      return const Color(0xFF92400E); // Yellow text for partial
    } else {
      return const Color(0xFF065F46); // Green text for completed
    }
  }

  /// Helper method to convert API attributes to table data format
  List<Map<String, String>> _getAttributeTableData(
      ObjectCreationModel? objectData) {
    if (objectData?.attributes == null || objectData!.attributes!.isEmpty) {
      return []; // Return empty list when no API data is available
    }

    return objectData.attributes!.map((attribute) {
      return {
        'name': attribute.name ?? '',
        'displayName': attribute.displayName ?? attribute.name ?? '',
        'dataType': attribute.dataType ?? 'string',
        'required': (attribute.required ?? false) ? 'YES' : 'NO',
        'unique': (attribute.unique ?? false) ? 'YES' : 'NO',
        'defaultType': attribute.defaultType?.isNotEmpty == true
            ? attribute.defaultType!
            : '',
        'defaultValue': attribute.defaultValue?.isNotEmpty == true
            ? attribute.defaultValue!
            : '',
        'description': attribute.description?.isNotEmpty == true
            ? attribute.description!
            : '',
        'helperText': attribute.helperText?.isNotEmpty == true
            ? attribute.helperText!
            : '',
        'enumValues': attribute.enumValues?.isNotEmpty == true
            ? attribute.enumValues!.join(', ')
            : '',
        'validation':
            attribute.validation?.required == true ? 'Required' : 'Optional',
      };
    }).toList();
  }

  /// Helper method to get relationships data for display
  List<List<String>> _getRelationshipsData(ObjectCreationModel? objectData) {
    if (objectData?.relationships == null ||
        objectData!.relationships!.isEmpty) {
      return []; // Return empty list if no relationships
    }

    return objectData.relationships!.map((relationship) {
      return [
        relationship.relatedEntity ?? '',
        relationship.relationshipType ?? '',
        relationship.foreignKey ?? '',
        relationship.description ?? '',
        'Active', // Default status
      ];
    }).toList();
  }

  /// Helper method to get business rules data for display
  List<List<String>> _getBusinessRulesData(ObjectCreationModel? objectData) {
    if (objectData?.businessRules == null ||
        objectData!.businessRules!.isEmpty) {
      return []; // Return empty list if no business rules
    }

    return objectData.businessRules!.map((rule) {
      return [
        rule.attributeName ?? '',
        rule.operator ?? '',
        rule.pattern ?? '',
        rule.errorMessage ?? '',
        'Active', // Default status
      ];
    }).toList();
  }

  /// Helper method to convert API relationships to table data format
  List<Map<String, String>> _getRelationshipTableData(
      ObjectCreationModel? objectData) {
    if (objectData?.relationships == null ||
        objectData!.relationships!.isEmpty) {
      return []; // Return empty list if no relationships
    }

    return objectData.relationships!.map((relationship) {
      return {
        'relatedEntity': relationship.relatedEntity ?? '',
        'relationshipType': relationship.relationshipType ?? '',
        'foreignKey': relationship.foreignKey ?? '',
        'description': relationship.description ?? '',
        'status': 'Active', // Default status
      };
    }).toList();
  }

  /// Helper method to convert API business rules to table data format
  List<Map<String, String>> _getBusinessRuleTableData(
      ObjectCreationModel? objectData) {
    if (objectData?.businessRules == null ||
        objectData!.businessRules!.isEmpty) {
      return []; // Return empty list if no business rules
    }

    return objectData.businessRules!.map((rule) {
      return {
        'attributeName': rule.attributeName ?? '',
        'operator': rule.operator ?? '',
        'value': rule.pattern ?? '',
        'errorMessage': rule.errorMessage ?? '',
        'status': 'Active', // Default status
      };
    }).toList();
  }

  /// Helper method to convert API enum values to table data format
  List<Map<String, String>> _getEnumValueTableData(
      ObjectCreationModel? objectData) {
    if (objectData?.enumValues == null || objectData!.enumValues!.isEmpty) {
      return []; // Return empty list if no enum values
    }

    return objectData.enumValues!.map((enumValue) {
      return {
        'entityAttribute': enumValue.entityAttribute ?? '',
        'enumName': enumValue.enumName ?? '',
        'value': enumValue.value ?? '',
        'display': enumValue.display ?? '',
        'description': enumValue.description ?? '',
        'sortOrder': enumValue.sortOrder?.toString() ?? '0',
        'active': (enumValue.active ?? true) ? 'Yes' : 'No',
      };
    }).toList();
  }

  /// Helper method to convert API security classification to table data format
  List<Map<String, String>> _getSecurityClassificationTableData(
      ObjectCreationModel? objectData) {
    if (objectData?.securityClassification == null ||
        objectData!.securityClassification!.isEmpty) {
      return []; // Return empty list if no security classification
    }

    return objectData.securityClassification!.map((security) {
      return {
        'entityAttribute': security.entityAttribute ?? '',
        'classification': security.classification ?? '',
        'piiType': security.piiType ?? 'none',
        'encryptionRequired':
            (security.encryptionRequired ?? false) ? 'Yes' : 'No',
        'encryptionType': security.encryptionType ?? 'none',
        'maskingRequired': (security.maskingRequired ?? false) ? 'Yes' : 'No',
        'maskingPattern': security.maskingPattern ?? '',
        'accessLevel': security.accessLevel ?? '',
        'auditTrail': (security.auditTrail ?? false) ? 'Yes' : 'No',
      };
    }).toList();
  }

  /// Helper method to convert API system permissions to table data format
  List<Map<String, String>> _getSystemPermissionTableData(
      ObjectCreationModel? objectData) {
    if (objectData?.systemPermissions == null ||
        objectData!.systemPermissions!.isEmpty) {
      return []; // Return empty list if no system permissions
    }

    return objectData.systemPermissions!.map((permission) {
      return {
        'permissionId': permission.permissionId ?? '',
        'permissionName': permission.permissionName ?? '',
        'permissionType': permission.permissionType ?? '',
        'resourceIdentifier': permission.resourceIdentifier ?? '',
        'actions': permission.actions?.join(', ') ?? '',
        'description': permission.description ?? '',
        'scope': permission.scope ?? '',
        'naturalLanguage': permission.naturalLanguage ?? '',
        'version': permission.version?.toString() ?? '1',
        'status': permission.status ?? 'active',
      };
    }).toList();
  }

  /// Helper method to convert API role system permissions to table data format
  List<Map<String, String>> _getRoleSystemPermissionTableData(
      ObjectCreationModel? objectData) {
    if (objectData?.roleSystemPermissions == null ||
        objectData!.roleSystemPermissions!.isEmpty) {
      return []; // Return empty list if no role system permissions
    }

    return objectData.roleSystemPermissions!.map((rolePermission) {
      return {
        'roleId': rolePermission.roleId ?? '',
        'permissionId': rolePermission.permissionId ?? '',
        'grantedActions': rolePermission.grantedActions?.join(', ') ?? '',
        'rowLevelConditions':
            rolePermission.rowLevelConditions?.toString() ?? '',
        'naturalLanguage': rolePermission.naturalLanguage ?? '',
      };
    }).toList();
  }

  /// Generic method to convert any API entity section to table data format
  List<Map<String, String>> _getGenericTableData(
      String sectionName, ObjectCreationModel? objectData) {
    if (objectData == null) {
      return [];
    }

    // Use existing specific methods for known sections
    switch (sectionName.toLowerCase()) {
      case 'attributes':
        return _getAttributeTableData(objectData);
      case 'relationships':
        return _getRelationshipTableData(objectData);
      case 'business_rules':
        return _getBusinessRuleTableData(objectData);
      case 'enum_values':
        return _getEnumValueTableData(objectData);
      case 'security_classification':
        return _getSecurityClassificationTableData(objectData);
      case 'system_permissions':
        return _getSystemPermissionTableData(objectData);
      case 'role_system_permissions':
        return _getRoleSystemPermissionTableData(objectData);
      default:
        // For unknown sections, try to extract data dynamically
        return _extractUnknownSectionData(sectionName, objectData);
    }
  }

  /// Extract data for unknown sections dynamically
  List<Map<String, String>> _extractUnknownSectionData(
      String sectionName, ObjectCreationModel objectData) {
    // This is a placeholder for future unknown sections
    // In a real implementation, you could use reflection or other dynamic approaches
    return [];
  }

  /// Helper method to get table data with API data support
  List<List<String>> _getTableDataWithApiSupport(String title,
      {ObjectCreationModel? objectData}) {
    switch (title) {
      case 'Entity Relationships':
        return _getRelationshipsData(objectData);
      case 'Attribute Business Rules':
        return _getBusinessRulesData(objectData);
      case 'Enumerated Values':
        // Convert Map data to List format for display
        final apiMapData = _getEnumValueTableData(objectData);
        return apiMapData
            .map((item) => [
                  item['entityAttribute'] ?? '',
                  item['enumName'] ?? '',
                  item['value'] ?? '',
                  item['display'] ?? '',
                  item['description'] ?? '',
                  item['sortOrder'] ?? '',
                  item['active'] ?? '',
                ])
            .toList();
      case 'Security Classification':
        // Convert Map data to List format for display
        final apiMapData = _getSecurityClassificationTableData(objectData);
        return apiMapData
            .map((item) => [
                  item['entityAttribute'] ?? '',
                  item['classification'] ?? '',
                  item['piiType'] ?? '',
                  item['encryptionRequired'] ?? '',
                  item['encryptionType'] ?? '',
                  item['maskingRequired'] ?? '',
                  item['maskingPattern'] ?? '',
                  item['accessLevel'] ?? '',
                  item['auditTrail'] ?? '',
                ])
            .toList();
      case 'System Permissions':
        // Convert Map data to List format for display
        final apiMapData = _getSystemPermissionTableData(objectData);
        return apiMapData
            .map((item) => [
                  item['permissionId'] ?? '',
                  item['permissionName'] ?? '',
                  item['permissionType'] ?? '',
                  item['resourceIdentifier'] ?? '',
                  item['actions'] ?? '',
                  item['description'] ?? '',
                  item['scope'] ?? '',
                  item['naturalLanguage'] ?? '',
                  item['version'] ?? '',
                  item['status'] ?? '',
                ])
            .toList();
      case 'Role System Permissions':
        // Convert Map data to List format for display
        final apiMapData = _getRoleSystemPermissionTableData(objectData);
        return apiMapData
            .map((item) => [
                  item['roleId'] ?? '',
                  item['permissionId'] ?? '',
                  item['grantedActions'] ?? '',
                  item['rowLevelConditions'] ?? '',
                  item['naturalLanguage'] ?? '',
                ])
            .toList();
      default:
        // For other sections, use the original method
        return _getTableData(title);
    }
  }

  /// Convert Map data to List format for table display
  List<List<String>> _convertMapDataToListFormat(
      List<Map<String, String>> mapData, List<String> headers) {
    return mapData.map((item) {
      return headers.map((header) {
        final key = _getKeyFromHeader(header);
        return item[key] ?? '';
      }).toList();
    }).toList();
  }

  /// Convert header back to key format
  String _getKeyFromHeader(String header) {
    // Create reverse mapping from header to key
    final headerToKeyMap = _getHeaderToKeyMapping();
    return headerToKeyMap[header] ?? header.toLowerCase();
  }

  /// Get comprehensive header to key mapping
  Map<String, String> _getHeaderToKeyMapping() {
    return {
      // Attributes headers
      'NAME': 'name',
      'DISPLAYNAME': 'displayName',
      'DATATYPE': 'dataType',
      'REQUIRED': 'required',
      'UNIQUE': 'unique',
      'DEFAULTTYPE': 'defaultType',
      'DEFAULTVALUE': 'defaultValue',
      'DESCRIPTION': 'description',
      'HELPERTEXT': 'helperText',
      'ENUMVALUES': 'enumValues',
      'VALIDATION': 'validation',

      // Relationships headers
      'RELATEDENTITY': 'relatedEntity',
      'RELATIONSHIPTYPE': 'relationshipType',
      'FOREIGNKEY': 'foreignKey',
      'STATUS': 'status',

      // Business Rules headers
      'ATTRIBUTENAME': 'attributeName',
      'OPERATOR': 'operator',
      'VALUE': 'value',
      'ERRORMESSAGE': 'errorMessage',

      // Enum Values headers
      'ENTITYATTRIBUTE': 'entityAttribute',
      'ENUMNAME': 'enumName',
      'DISPLAY': 'display',
      'SORTORDER': 'sortOrder',
      'ACTIVE': 'active',

      // Security Classification headers
      'CLASSIFICATION': 'classification',
      'PIITYPE': 'piiType',
      'ENCRYPTIONREQUIRED': 'encryptionRequired',
      'ENCRYPTIONTYPE': 'encryptionType',
      'MASKINGREQUIRED': 'maskingRequired',
      'MASKINGPATTERN': 'maskingPattern',
      'ACCESSLEVEL': 'accessLevel',
      'AUDITTRAIL': 'auditTrail',

      // System Permissions headers
      'PERMISSIONID': 'permissionId',
      'PERMISSIONNAME': 'permissionName',
      'PERMISSIONTYPE': 'permissionType',
      'RESOURCEIDENTIFIER': 'resourceIdentifier',
      'ACTIONS': 'actions',
      'SCOPE': 'scope',
      'NATURALLANGUAGE': 'naturalLanguage',
      'VERSION': 'version',

      // Role System Permissions headers
      'ROLEID': 'roleId',
      'GRANTEDACTIONS': 'grantedActions',
      'ROWLEVELCONDITIONS': 'rowLevelConditions',
    };
  }

  /// Helper method to get dynamic column headers from API data
  List<String> _getDynamicAttributeHeaders(ObjectCreationModel? objectData) {
    final sampleData = _getAttributeTableData(objectData);
    if (sampleData.isEmpty) {
      return [
        'NAME',
        'DISPLAYNAME',
        'DATATYPE',
        'REQUIRED',
        'UNIQUE',
        'DEFAULTTYPE',
        'DEFAULTVALUE',
        'DESCRIPTION',
        'HELPERTEXT',
        'ENUMVALUES',
        'VALIDATION'
      ];
    }

    // Get keys from the first data entry and convert to uppercase
    // Ensure consistent order for better UX
    final keyOrder = [
      'name',
      'displayName',
      'dataType',
      'required',
      'unique',
      'defaultType',
      'defaultValue',
      'description',
      'helperText',
      'enumValues',
      'validation'
    ];
    final availableKeys = sampleData.first.keys.toSet();

    // Return keys in preferred order, then any additional keys
    final orderedKeys = <String>[];
    for (final key in keyOrder) {
      if (availableKeys.contains(key)) {
        orderedKeys.add(key.toUpperCase());
      }
    }

    // Add any remaining keys not in the preferred order
    for (final key in availableKeys) {
      if (!keyOrder.contains(key)) {
        orderedKeys.add(key.toUpperCase());
      }
    }

    return orderedKeys;
  }

  /// Generic method to get dynamic headers for any entity section
  List<String> _getDynamicHeadersForSection(
      String sectionName, ObjectCreationModel? objectData) {
    final sampleData = _getGenericTableData(sectionName, objectData);
    if (sampleData.isEmpty) {
      return _getFallbackHeaders(sectionName);
    }

    // Get keys from the first data entry and convert to uppercase
    final keyOrder = _getPreferredKeyOrder(sectionName);
    final availableKeys = sampleData.first.keys.toSet();

    final orderedKeys = <String>[];

    // Add keys in preferred order first
    for (final key in keyOrder) {
      if (availableKeys.contains(key)) {
        orderedKeys.add(key.toUpperCase());
      }
    }

    // Add any remaining keys not in the preferred order
    for (final key in availableKeys) {
      if (!keyOrder.contains(key)) {
        orderedKeys.add(key.toUpperCase());
      }
    }

    return orderedKeys;
  }

  /// Get preferred key order for different entity sections
  List<String> _getPreferredKeyOrder(String sectionName) {
    switch (sectionName.toLowerCase()) {
      case 'attributes':
        return [
          'name',
          'displayName',
          'dataType',
          'required',
          'unique',
          'defaultType',
          'defaultValue',
          'description',
          'helperText',
          'enumValues',
          'validation'
        ];
      case 'relationships':
        return [
          'relatedEntity',
          'relationshipType',
          'foreignKey',
          'description',
          'status'
        ];
      case 'business_rules':
        return ['attributeName', 'operator', 'value', 'errorMessage', 'status'];
      case 'enum_values':
        return [
          'entityAttribute',
          'enumName',
          'value',
          'display',
          'description',
          'sortOrder',
          'active'
        ];
      case 'security_classification':
        return [
          'entityAttribute',
          'classification',
          'piiType',
          'encryptionRequired',
          'encryptionType',
          'maskingRequired',
          'maskingPattern',
          'accessLevel',
          'auditTrail'
        ];
      case 'system_permissions':
        return [
          'permissionId',
          'permissionName',
          'permissionType',
          'resourceIdentifier',
          'actions',
          'description',
          'scope',
          'naturalLanguage',
          'version',
          'status'
        ];
      case 'role_system_permissions':
        return [
          'roleId',
          'permissionId',
          'grantedActions',
          'rowLevelConditions',
          'naturalLanguage'
        ];
      default:
        return [];
    }
  }

  /// Get fallback headers when no data is available
  List<String> _getFallbackHeaders(String sectionName) {
    switch (sectionName.toLowerCase()) {
      case 'attributes':
        return [
          'NAME',
          'DISPLAYNAME',
          'DATATYPE',
          'REQUIRED',
          'UNIQUE',
          'DEFAULTTYPE',
          'DEFAULTVALUE',
          'DESCRIPTION',
          'HELPERTEXT',
          'ENUMVALUES',
          'VALIDATION'
        ];
      case 'relationships':
        return [
          'RELATEDENTITY',
          'RELATIONSHIPTYPE',
          'FOREIGNKEY',
          'DESCRIPTION',
          'STATUS'
        ];
      case 'business_rules':
        return ['ATTRIBUTENAME', 'OPERATOR', 'VALUE', 'ERRORMESSAGE', 'STATUS'];
      case 'enum_values':
        return [
          'ENTITYATTRIBUTE',
          'ENUMNAME',
          'VALUE',
          'DISPLAY',
          'DESCRIPTION',
          'SORTORDER',
          'ACTIVE'
        ];
      case 'security_classification':
        return [
          'ENTITYATTRIBUTE',
          'CLASSIFICATION',
          'PIITYPE',
          'ENCRYPTIONREQUIRED',
          'ENCRYPTIONTYPE',
          'MASKINGREQUIRED',
          'MASKINGPATTERN',
          'ACCESSLEVEL',
          'AUDITTRAIL'
        ];
      case 'system_permissions':
        return [
          'PERMISSIONID',
          'PERMISSIONNAME',
          'PERMISSIONTYPE',
          'RESOURCEIDENTIFIER',
          'ACTIONS',
          'DESCRIPTION',
          'SCOPE',
          'NATURALLANGUAGE',
          'VERSION',
          'STATUS'
        ];
      case 'role_system_permissions':
        return [
          'ROLEID',
          'PERMISSIONID',
          'GRANTEDACTIONS',
          'ROWLEVELCONDITIONS',
          'NATURALLANGUAGE'
        ];
      default:
        return ['NAME', 'VALUE', 'TYPE', 'STATUS'];
    }
  }

  /// Calculate dynamic column width based on actual text measurement
  double _calculateDynamicColumnWidth(String headerText, String sectionName,
      {List<String>? sampleData}) {
    // Create TextPainter to measure text dimensions with more generous styling
    final textPainter = TextPainter(
      text: TextSpan(
        text: headerText,
        style: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          fontFamily: 'Inter',
          letterSpacing: 0.5, // Add letter spacing for better measurement
        ),
      ),
      textDirection: TextDirection.ltr,
      maxLines: 1, // Ensure single line measurement
    );

    textPainter.layout();
    double headerWidth = textPainter.size.width;

    // Debug logging for troubleshooting
    print('🔍 Header: "$headerText" - Measured width: ${headerWidth}px');

    // Consider sample data content if provided
    double contentWidth = headerWidth;
    if (sampleData != null && sampleData.isNotEmpty) {
      double maxContentWidth = 0;
      for (String content in sampleData.take(3)) {
        // Check first 3 samples
        final contentPainter = TextPainter(
          text: TextSpan(
            text: content,
            style: const TextStyle(
              fontSize: 12,
              fontFamily: 'Inter',
            ),
          ),
          textDirection: TextDirection.ltr,
          maxLines: 1,
        );
        contentPainter.layout();
        maxContentWidth = math.max(maxContentWidth, contentPainter.size.width);
      }
      contentWidth = math.max(headerWidth, maxContentWidth);
    }

    // Add more generous padding for headers (50px total - 25px each side)
    // This accounts for table cell padding, borders, and any additional styling
    double calculatedWidth = contentWidth + 50;

    // Apply min/max constraints based on header type
    final constraints = _getColumnConstraints(headerText, sectionName);

    // For very long headers, be more generous with the minimum width
    double finalWidth = math.max(
        constraints.minWidth, math.min(constraints.maxWidth, calculatedWidth));

    // Special handling for extremely long headers (>15 characters)
    if (headerText.length > 15) {
      finalWidth = math.max(finalWidth,
          headerText.length * 8.5); // Rough character width estimation
    }

    print('🔍 Final width for "$headerText": ${finalWidth}px');
    return finalWidth;
  }

  /// Get column constraints based on header type and section
  ColumnConstraints _getColumnConstraints(
      String headerText, String sectionName) {
    // Default constraints
    double minWidth = 100.0; // Increased default minimum
    double maxWidth = 350.0; // Increased default maximum

    // Special handling for Security Classification section
    if (sectionName == 'security_classification') {
      // Security headers tend to be very long, so be more generous
      minWidth = 120.0;
      maxWidth = 500.0; // Much higher max for security headers

      // Specific security header adjustments
      if (headerText.contains('ENCRYPTION') || headerText.contains('MASKING')) {
        minWidth = 150.0;
        maxWidth = 600.0;
      }
    }

    // Adjust based on header type
    if (headerText.contains('DESCRIPTION') ||
        headerText.contains('NATURALLANGUAGE')) {
      minWidth = math.max(minWidth, 150.0);
      maxWidth = math.max(maxWidth, 400.0);
    } else if (headerText.contains('ID') ||
        headerText.contains('VERSION') ||
        headerText.contains('ACTIVE')) {
      minWidth = 60.0;
      maxWidth = 120.0;
    } else if (headerText.contains('REQUIRED') ||
        headerText.contains('UNIQUE') ||
        headerText.contains('ENCRYPTION') ||
        headerText.contains('MASKING')) {
      minWidth = math.max(minWidth, 120.0); // Increased for these types
      maxWidth = math.max(maxWidth, 200.0);
    } else if (headerText.contains('ACTIONS') ||
        headerText.contains('CONDITIONS')) {
      minWidth = math.max(minWidth, 120.0);
      maxWidth = math.max(maxWidth, 250.0);
    }

    // Additional safety for very long headers
    if (headerText.length > 20) {
      minWidth = math.max(minWidth, headerText.length * 7.0);
      maxWidth = math.max(maxWidth, headerText.length * 10.0);
    }

    return ColumnConstraints(minWidth: minWidth, maxWidth: maxWidth);
  }

  /// Get dynamic column widths for any section with intelligent distribution
  List<double> _getDynamicColumnWidthsForSection(
      String sectionName, List<String> headers,
      {double? availableWidth, List<Map<String, String>>? sampleData}) {
    // Calculate individual column widths
    List<double> calculatedWidths = [];

    for (int i = 0; i < headers.length; i++) {
      String header = headers[i];
      List<String>? columnSampleData;

      // Extract sample data for this column if available
      if (sampleData != null && sampleData.isNotEmpty) {
        final key = _getKeyFromHeader(header);
        columnSampleData = sampleData
            .map((row) => row[key] ?? '')
            .where((value) => value.isNotEmpty)
            .take(3)
            .toList();
      }

      double width = _calculateDynamicColumnWidth(header, sectionName,
          sampleData: columnSampleData);
      calculatedWidths.add(width);
    }

    // Apply intelligent distribution if total width exceeds available space
    if (availableWidth != null) {
      double totalCalculatedWidth = calculatedWidths.reduce((a, b) => a + b);

      print(
          '🔍 Total calculated width: ${totalCalculatedWidth}px, Available: ${availableWidth}px');

      if (totalCalculatedWidth > availableWidth) {
        // Use a more intelligent scaling approach
        // First, try to preserve the widest columns (likely the most important)
        // and scale down smaller columns more aggressively

        List<double> minWidths = headers
            .map(
                (header) => _getColumnConstraints(header, sectionName).minWidth)
            .toList();

        double totalMinWidth = minWidths.reduce((a, b) => a + b);

        if (totalMinWidth <= availableWidth) {
          // We can fit all columns at their minimum widths
          double remainingWidth = availableWidth - totalMinWidth;
          double totalExtraWidth = totalCalculatedWidth - totalMinWidth;

          if (totalExtraWidth > 0) {
            // Distribute remaining width proportionally to extra width
            for (int i = 0; i < calculatedWidths.length; i++) {
              double extraWidth = calculatedWidths[i] - minWidths[i];
              double proportionalExtra =
                  (extraWidth / totalExtraWidth) * remainingWidth;
              calculatedWidths[i] = minWidths[i] + proportionalExtra;
            }
          } else {
            // Just use minimum widths
            calculatedWidths = minWidths;
          }
        } else {
          // Even minimum widths don't fit, use simple proportional scaling
          double scaleFactor = availableWidth / totalCalculatedWidth;
          for (int i = 0; i < calculatedWidths.length; i++) {
            calculatedWidths[i] = calculatedWidths[i] * scaleFactor;
          }
        }

        print(
            '🔍 After scaling: ${calculatedWidths.map((w) => w.toStringAsFixed(1)).join(', ')}');
      }
    }

    return calculatedWidths;
  }

  /// Fallback method for backward compatibility
  Map<String, double> _getDynamicColumnWidths() {
    // This method is kept for backward compatibility
    // New implementations should use _getDynamicColumnWidthsForSection
    return {
      'NAME': 150.0,
      'DISPLAYNAME': 200.0,
      'DATATYPE': 120.0,
      'REQUIRED': 100.0,
      'UNIQUE': 100.0,
      'DEFAULTTYPE': 150.0,
      'DEFAULTVALUE': 150.0,
      'DESCRIPTION': 250.0,
      'HELPERTEXT': 200.0,
      'ENUMVALUES': 200.0,
      'VALIDATION': 120.0,
      'RELATEDENTITY': 180.0,
      'RELATIONSHIPTYPE': 150.0,
      'FOREIGNKEY': 120.0,
      'STATUS': 100.0,
      'ATTRIBUTENAME': 150.0,
      'OPERATOR': 120.0,
      'VALUE': 150.0,
      'ERRORMESSAGE': 200.0,
      'ENTITYATTRIBUTE': 150.0,
      'ENUMNAME': 120.0,
      'DISPLAY': 120.0,
      'SORTORDER': 80.0,
      'ACTIVE': 80.0,
      'CLASSIFICATION': 120.0,
      'PIITYPE': 100.0,
      'ENCRYPTIONREQUIRED': 120.0,
      'ENCRYPTIONTYPE': 120.0,
      'MASKINGREQUIRED': 120.0,
      'MASKINGPATTERN': 120.0,
      'ACCESSLEVEL': 120.0,
      'AUDITTRAIL': 100.0,
      'PERMISSIONID': 150.0,
      'PERMISSIONNAME': 150.0,
      'PERMISSIONTYPE': 120.0,
      'RESOURCEIDENTIFIER': 150.0,
      'ACTIONS': 150.0,
      'SCOPE': 120.0,
      'NATURALLANGUAGE': 180.0,
      'VERSION': 80.0,
      'ROLEID': 120.0,
      'GRANTEDACTIONS': 200.0,
      'ROWLEVELCONDITIONS': 200.0,
    };
  }

  /// Helper method to build dynamic header row
  Widget _buildDynamicHeaderRow(
      BuildContext context, ObjectCreationModel? objectData) {
    final headers = _getDynamicAttributeHeaders(objectData);
    final widths = _getDynamicColumnWidths();

    return Row(
      children: headers.map((header) {
        final width = widths[header] ?? 150.0;
        return SizedBox(
          width: width,
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              header,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.labelMedium(context),
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey[700],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  /// Helper method to build dynamic attribute row
  Widget _buildDynamicAttributeRow(
    BuildContext context,
    int index,
    Map<String, String> data,
    ObjectCreationModel? objectData,
  ) {
    final headers = _getDynamicAttributeHeaders(objectData);
    final widths = _getDynamicColumnWidths();

    // Use the generic header to key mapping
    final headerToKeyMap = _getHeaderToKeyMapping();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: headers.map((header) {
          final width = widths[header] ?? 150.0;
          final key = headerToKeyMap[header] ?? header.toLowerCase();
          final value = data[key] ?? '';

          return SizedBox(
            width: width,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                value,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<WebHomeProviderStatic, ObjectCreationProvider>(
      builder: (context, provider, objectCreationProvider, child) {
        return Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.white,
          child: Column(
            children: [
              // Header with toggle
              _buildHeader(context, provider),

              // Content area
              Expanded(
                child: _buildContent(context, provider),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, WebHomeProviderStatic provider) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: const BoxDecoration(
        color: Colors.black,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Dynamic header label based on toggle state
          Text(
            // provider.isAIMode ? 'Objects' : 'Extracted Details',
            'Extracted Details',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleSmall(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.white,
              fontWeight: FontWeight.bold,
              height: 1,
            ),
          ),

          // Right side with toggle and manually process text
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // AI/Manual Toggle
              _buildAIManualToggle(context, provider),

              const SizedBox(width: 16),

              // // Manually Process text
              // Text(
              //   'Manually Process',
              //   style: FontManager.getCustomStyle(
              //     fontSize: ResponsiveFontSizes.titleSmall(context),
              //     color: Colors.white,
              //     fontWeight: FontWeight.w400,
              //     fontFamily: FontManager.fontFamilyTiemposText,
              //     height: 1,
              //   ),
              // ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAIManualToggle(
      BuildContext context, WebHomeProviderStatic provider) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // manua; label on the left
        Text(
          'Manual Process',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            color: Colors.white,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            height: 1,
          ),
        ),
        const SizedBox(width: 8),

        // Toggle switch
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              // Close entity details panel if it's open before switching modes
              final manualProvider =
                  Provider.of<ManualCreationProvider>(context, listen: false);
              if (manualProvider.selectedEntity != null) {
                manualProvider.setSelectedEntity(null);
              }

              provider.toggleAIMode();
              manualProvider.handleEntityValidationForBook();
            },
            child: Container(
              width: 34,
              height: 18,
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(
                  color: Colors.black,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(10),
              ),
              child: AnimatedAlign(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeInOut,
                alignment: provider.isAIMode
                    ? Alignment.centerLeft
                    : Alignment.centerRight,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Color(0xFF0058FF),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
          ),
        ),

        const SizedBox(width: 8),

        // ai label on the right
        Text(
          'AI',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            color: Colors.white,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            height: 1,
          ),
        ),
      ],
    );
  }

  Widget _buildContent(BuildContext context, WebHomeProviderStatic provider) {
    return _buildExtractedDetailsTab(context);

    // if (provider.isAIMode) {
    //   return _buildObjectsTab(context);
    // } else {
    //   return _buildExtractedDetailsTab(context);
    // }
  }

  Widget _buildExtractedDetailsTab(BuildContext context) {
    return Consumer<ObjectCreationProvider>(
      builder: (context, objectCreationProvider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(6.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Show loading indicator while fetching API data
              if (objectCreationProvider.isLoadingExtraction ||
                  objectCreationProvider.isLoadingJobEntities)
                Container(
                  padding: const EdgeInsets.all(20),
                  child: Center(
                    child: Column(
                      children: [
                        const CircularProgressIndicator(),
                        const SizedBox(height: 16),
                        Text(
                          'Loading objects...',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              // Show error message if API call failed
              if (objectCreationProvider.error != null)
                Container(
                  padding: const EdgeInsets.all(20),
                  child: Center(
                    child: Column(
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 48,
                          color: Colors.red[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Error loading objects',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.titleMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.red[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          objectCreationProvider.error!,
                          textAlign: TextAlign.center,
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadApiData,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF0058FF),
                            foregroundColor: Colors.white,
                          ),
                          child: Text(
                            'Retry',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              // Show API objects if available
              if (!objectCreationProvider.isLoadingExtraction &&
                  !objectCreationProvider.isLoadingJobEntities &&
                  objectCreationProvider.error == null &&
                  objectCreationProvider.hasObjects)
                ...objectCreationProvider.objects
                    .map((object) => _buildObjectExpansionPanel(context,
                        'Object: ${object.displayName ?? object.name ?? ''}',
                        objectData: object))
                    .toList(),

              // Show empty state when no API data is available
              if (!objectCreationProvider.isLoadingExtraction &&
                  !objectCreationProvider.isLoadingJobEntities &&
                  objectCreationProvider.error == null &&
                  !objectCreationProvider.hasObjects)
                Container(
                  padding: const EdgeInsets.all(40),
                  child: Center(
                    child: Column(
                      children: [
                        Icon(
                          Icons.inbox_outlined,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No Objects Found',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.titleMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'The API did not return any objects for the provided input. Please try with different parameters or check your input.',
                          textAlign: TextAlign.center,
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildObjectsTab(BuildContext context) {
    return const ManualProcessingComponent();
  }

  Widget _buildObjectExpansionPanel(BuildContext context, String objectTitle,
      {ObjectCreationModel? objectData}) {
    return Consumer<WebHomeProviderStatic>(
      builder: (context, provider, child) {
        final isExpanded = provider.isObjectExpanded(objectTitle);

        return Container(
          margin: const EdgeInsets.symmetric(vertical: 1),
          decoration: BoxDecoration(
            color: Colors.white,
            // border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
            // borderRadius: BorderRadius.circular(4),
          ),
          child: Theme(
            data: Theme.of(context).copyWith(
              dividerColor: Colors.transparent,
            ),
            child: ListTileTheme(
              dense: true, // slightly tighter by default
              child: ExpansionTile(
                tilePadding:
                    const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
                childrenPadding: EdgeInsets.zero,
                onExpansionChanged: (expanded) =>
                    provider.setObjectExpansion(objectTitle, expanded),
                trailing: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: isExpanded
                        ? const Color(0xFF0058FF)
                        : Colors.transparent,
                  ),
                  child: Icon(
                    isExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: isExpanded ? Colors.white : Colors.grey[600],
                    size: 20,
                  ),
                ),
                title: Row(
                  children: [
                    Expanded(
                      child: Text(
                        objectTitle,
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.titleMedium(context),
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                          fontWeight: FontWeight.w600,
                          height: 1.2,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    HoverBellIcon(
                      onTap: () {
                        // Bell icon click action - can be customized as needed
                      },
                    ),
                  ],
                ),
                children: [
                  _buildObjectDetailsSection(context, objectTitle,
                      objectData: objectData),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Books accordion style - recreated from global_library_accordion_flow
  Widget _buildBooksAccordionItem(AccordionItem item) {
    final isExpanded = _accordionController.isPanelExpanded(item.id);

    // Get status and count based on item title
    final statusInfo = _getStatusInfo(item.title);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
      decoration: BoxDecoration(
        border: Border.all(
            color:
                isExpanded ? const Color(0xFF0058FF) : const Color(0xFFE5E7EB),
            width: isExpanded ? 2 : 1),
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Column(
        children: [
          // Title row
          InkWell(
            onTap: () {
              _accordionController.togglePanel(item.id);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              decoration: const BoxDecoration(
                color: Colors.white,
              ),
              child: Row(
                children: [
                  // Title
                  Expanded(
                    flex: 3,
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            item.title,
                            style: FontManager.getCustomStyle(
                              fontSize:
                                  ResponsiveFontSizes.titleMedium(context),
                              fontWeight: isExpanded
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                        const SizedBox(width: 8),
                        // Status badge positioned on the right
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: statusInfo['backgroundColor'],
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            statusInfo['status'],
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.labelSmall(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: statusInfo['textColor'],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Count
                  Container(
                    width: 100,
                    alignment: Alignment.centerRight,
                    child: Text(
                      statusInfo['count'],
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Arrow icon
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey[600],
                      size: 20,
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Expandable content
          if (isExpanded) ...[
            Container(
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
                color: Colors.white,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Show subtitle as content if available
                  if (item.subtitle != null) ...[
                    Container(
                      padding: EdgeInsets.all(16),
                      child: Text(
                        item.subtitle!,
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyInter,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                  ] else ...[
                    // Show children items if no subtitle
                    ...item.children.map(
                      (child) => Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        child: Row(
                          children: [
                            Container(
                              width: 4,
                              height: 4,
                              decoration: BoxDecoration(
                                color: Colors.grey[400],
                                shape: BoxShape.circle,
                              ),
                            ),
                            SizedBox(width: 12),
                            Text(
                              child,
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w400,
                                fontFamily: FontManager.fontFamilyInter,
                                color: Colors.grey[700],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                  // Show nested accordion items
                  _buildNestedAccordionItems(context),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildObjectDetailsSection(BuildContext context, String objectTitle,
      {ObjectCreationModel? objectData}) {
    // Extract data from API response or use defaults
    final attributesCount = objectData?.attributes?.length ?? 0;
    final relationshipsCount = objectData?.relationships?.length ?? 0;
    final businessRulesCount = objectData?.businessRules?.length ?? 0;
    final enumValuesCount = objectData?.enumValues?.length ?? 0;
    final systemPermissionsCount = objectData?.systemPermissions?.length ?? 0;
    final securityClassificationCount =
        objectData?.securityClassification?.length ?? 0;

    return Container(
      margin: const EdgeInsets.fromLTRB(12, 0, 4, 0),
      child: Column(
        children: [
          // Object Details accordion
          _buildSimpleAccordionItem(
            context,
            'Object Details',
            'Partial Completion',
            '3 Entity Detected',
            const Color(0xFFFEF3C7),
            const Color(0xFF92400E),
            false,
            objectData: objectData,
          ),

          // Attributes Details accordion with table
          _buildSimpleAccordionItem(
            context,
            'Attributes Details',
            _getStatusFromCount(attributesCount),
            '$attributesCount Attributes',
            _getBackgroundColorFromCount(attributesCount),
            _getTextColorFromCount(attributesCount),
            true,
            objectData: objectData,
          ),

          // Other accordion items
          _buildSimpleAccordionItem(
            context,
            'Entity Relationships',
            _getStatusFromCount(relationshipsCount),
            '$relationshipsCount rules Configured',
            _getBackgroundColorFromCount(relationshipsCount),
            _getTextColorFromCount(relationshipsCount),
            false,
            objectData: objectData,
          ),
          _buildSimpleAccordionItem(
            context,
            'Attribute Business Rules',
            _getStatusFromCount(businessRulesCount),
            '$businessRulesCount Configure',
            _getBackgroundColorFromCount(businessRulesCount),
            _getTextColorFromCount(businessRulesCount),
            false,
            objectData: objectData,
          ),
          _buildSimpleAccordionItem(
            context,
            'Enumerated Values',
            _getStatusFromCount(enumValuesCount),
            '$enumValuesCount Configure',
            _getBackgroundColorFromCount(enumValuesCount),
            _getTextColorFromCount(enumValuesCount),
            false,
            objectData: objectData,
          ),
          _buildSimpleAccordionItem(
            context,
            'System Permissions',
            _getStatusFromCount(systemPermissionsCount),
            '$systemPermissionsCount Configure',
            _getBackgroundColorFromCount(systemPermissionsCount),
            _getTextColorFromCount(systemPermissionsCount),
            false,
            objectData: objectData,
          ),
          _buildSimpleAccordionItem(
            context,
            'Security Classification',
            _getStatusFromCount(securityClassificationCount),
            '$securityClassificationCount Configure',
            _getBackgroundColorFromCount(securityClassificationCount),
            _getTextColorFromCount(securityClassificationCount),
            false,
            objectData: objectData,
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleAccordionItem(
    BuildContext context,
    String title,
    String status,
    String count,
    Color backgroundColor,
    Color textColor,
    bool showAttributeTable, {
    ObjectCreationModel? objectData,
  }) {
    final isExpanded = _accordionController.isPanelExpanded('simple_$title');

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        border: Border.all(
          color: isExpanded ? const Color(0xFF0058FF) : const Color(0xFFE5E7EB),
          width: isExpanded ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              _accordionController.togglePanel('simple_$title');
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              child: Row(
                children: [
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Title Text - Allow it to take available space but don't force ellipsis
                        Flexible(
                          flex: 3,
                          child: Text(
                            title,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: isExpanded
                                  ? FontWeight.w600
                                  : FontWeight.w300,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow
                                .visible, // Allow text to show fully
                            maxLines: 2, // Allow wrapping to 2 lines if needed
                          ),
                        ),

                        const SizedBox(width: 8),

                        // Status Badge - Give it fixed space
                        Flexible(
                          flex: 2,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: backgroundColor,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              status,
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.labelSmall(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: textColor,
                              ),
                              overflow: TextOverflow.visible,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),
                  Container(
                    width: 100,
                    alignment: Alignment.centerRight,
                    child: Text(
                      count,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey[600],
                      size: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isExpanded) ...[
            Container(
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
                color: Colors.white,
              ),
              child: showAttributeTable
                  ? _buildAttributeConfigurationTable(context,
                      objectData: objectData)
                  : _buildPlaceholderContent(context, title,
                      objectData: objectData),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildNestedAccordionItems(BuildContext context,
      {ObjectCreationModel? objectData}) {
    // Extract data from API response or use defaults
    final attributesCount = objectData?.attributes?.length ?? 0;
    final relationshipsCount = objectData?.relationships?.length ?? 0;
    final businessRulesCount = objectData?.businessRules?.length ?? 0;
    final enumValuesCount = objectData?.enumValues?.length ?? 0;
    final systemPermissionsCount = objectData?.systemPermissions?.length ?? 0;
    final securityClassificationCount =
        objectData?.securityClassification?.length ?? 0;

    return Column(
      children: [
        _buildNestedAccordionItem(
          context,
          'Attributes Details',
          _getStatusFromCount(attributesCount),
          '$attributesCount Attributes',
          _getBackgroundColorFromCount(attributesCount),
          _getTextColorFromCount(attributesCount),
          true,
          objectData: objectData,
        ),
        _buildNestedAccordionItem(
          context,
          'Entity Relationships',
          _getStatusFromCount(relationshipsCount),
          '$relationshipsCount rules Configured',
          _getBackgroundColorFromCount(relationshipsCount),
          _getTextColorFromCount(relationshipsCount),
          false,
          objectData: objectData,
        ),
        _buildNestedAccordionItem(
          context,
          'Attribute Business Rules',
          _getStatusFromCount(businessRulesCount),
          '$businessRulesCount Configure',
          _getBackgroundColorFromCount(businessRulesCount),
          _getTextColorFromCount(businessRulesCount),
          false,
          objectData: objectData,
        ),
        _buildNestedAccordionItem(
          context,
          'Enumerated Values',
          _getStatusFromCount(enumValuesCount),
          '$enumValuesCount Configure',
          _getBackgroundColorFromCount(enumValuesCount),
          _getTextColorFromCount(enumValuesCount),
          false,
          objectData: objectData,
        ),
        _buildNestedAccordionItem(
          context,
          'System Permissions',
          _getStatusFromCount(systemPermissionsCount),
          '$systemPermissionsCount Configure',
          _getBackgroundColorFromCount(systemPermissionsCount),
          _getTextColorFromCount(systemPermissionsCount),
          false,
          objectData: objectData,
        ),
        _buildNestedAccordionItem(
          context,
          'Security Classification',
          _getStatusFromCount(securityClassificationCount),
          '$securityClassificationCount Configure',
          _getBackgroundColorFromCount(securityClassificationCount),
          _getTextColorFromCount(securityClassificationCount),
          false,
          objectData: objectData,
        ),
      ],
    );
  }

  Widget _buildNestedAccordionItem(
    BuildContext context,
    String title,
    String status,
    String count,
    Color backgroundColor,
    Color textColor,
    bool showAttributeTable, {
    ObjectCreationModel? objectData,
  }) {
    final isExpanded = _accordionController.isPanelExpanded('nested_$title');

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 1),
      decoration: BoxDecoration(
        border: Border.all(
          color: isExpanded ? const Color(0xFF0058FF) : const Color(0xFFE5E7EB),
          width: isExpanded ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              _accordionController.togglePanel('nested_$title');
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              child: Row(
                children: [
                  // Title and Status Badge section
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Title Text - Allow it to take available space
                        Flexible(
                          flex: 3,
                          child: Text(
                            title,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: isExpanded
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.visible,
                            maxLines: 2,
                          ),
                        ),
                        const SizedBox(width: 12),
                        // Status Badge - Give it fixed space
                        Flexible(
                          flex: 2,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: backgroundColor,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              status,
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.labelSmall(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: textColor,
                              ),
                              overflow: TextOverflow.visible,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Count
                  Text(
                    count,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelSmall(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Arrow icon
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey[600],
                      size: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isExpanded && showAttributeTable) ...[
            Container(
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
                color: Colors.white,
              ),
              child: _buildAttributeConfigurationTable(context,
                  objectData: objectData),
            ),
          ],
          if (isExpanded && title == 'Entity Relationships') ...[
            Container(
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
                color: Colors.white,
              ),
              child: _buildEntityRelationshipTable(context,
                  objectData: objectData),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEntityRelationshipTable(BuildContext context,
      {ObjectCreationModel? objectData}) {
    return Container(
      padding: const EdgeInsets.all(6),
      margin: const EdgeInsets.symmetric(horizontal: 6),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and Add Entity Relationship button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Entity Relationship Configuration',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              IgnorePointer(
                ignoring: !Provider.of<WebHomeProviderStatic>(context).isAIMode,
                child: Opacity(
                  opacity: !Provider.of<WebHomeProviderStatic>(context).isAIMode
                      ? 0.5
                      : 1.0,
                  child: ElevatedButton.icon(
                    onPressed: () => _showAddEntityRelationshipModal(context),
                    icon: const Icon(Icons.link, size: 16),
                    label: const Text('Add Entity Relationship'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0058FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 6),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                      elevation: 0,
                      textStyle: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Table container with scrollable data columns and fixed Actions column
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFE5E7EB)),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Row(
              children: [
                // Scrollable data columns section
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Column(
                      children: [
                        // Header row
                        Container(
                          decoration: const BoxDecoration(
                            color: Color(0xFFF9FAFB),
                            border: Border(
                              bottom: BorderSide(
                                  color: Color(0xFFE5E7EB), width: 1),
                            ),
                          ),
                          child: Row(
                            children: [
                              _buildHeaderCell(context, 'RELATED ENTITY', 180),
                              _buildHeaderCell(
                                  context, 'RELATIONSHIP TYPE', 150),
                              _buildHeaderCell(context, 'FOREIGN KEY', 120),
                              _buildHeaderCell(context, 'DESCRIPTION', 200),
                              _buildHeaderCell(context, 'STATUS', 100),
                            ],
                          ),
                        ),
                        // Data rows
                        if (_relationshipData.isEmpty)
                          Container(
                            height: 100,
                            decoration: const BoxDecoration(
                              color: Colors.white,
                            ),
                            child: const Center(
                              child: Text(
                                'No relationships configured yet',
                                style: TextStyle(
                                  color: Colors.grey,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          )
                        else
                          ...List.generate(_relationshipData.length, (index) {
                            final relationship = _relationshipData[index];
                            return _buildRelationshipDataRow(
                                context, index, relationship);
                          }),
                      ],
                    ),
                  ),
                ),
                // Fixed Actions column
                Container(
                  width: 100,
                  decoration: const BoxDecoration(
                    border: Border(
                      left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                    ),
                  ),
                  child: Column(
                    children: [
                      // Actions header
                      Container(
                        height: 40,
                        decoration: const BoxDecoration(
                          color: Color(0xFFF9FAFB),
                          border: Border(
                            bottom:
                                BorderSide(color: Color(0xFFE5E7EB), width: 1),
                          ),
                        ),
                        child: Center(
                          child: Text(
                            'ACTIONS',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.labelSmall(context),
                              fontWeight: FontWeight.w600,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ),
                      // Action buttons for each row
                      if (_relationshipData.isEmpty)
                        Container(
                          height: 100,
                          decoration: const BoxDecoration(
                            color: Colors.white,
                          ),
                        )
                      else
                        ...List.generate(_relationshipData.length, (index) {
                          return Container(
                            height: 50,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              border: index < _relationshipData.length - 1
                                  ? const Border(
                                      bottom: BorderSide(
                                          color: Color(0xFFE5E7EB), width: 1),
                                    )
                                  : null,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                IconButton(
                                  onPressed: () =>
                                      _editRelationshipInPlace(context, index),
                                  icon: const Icon(Icons.edit_outlined),
                                  color: Colors.blue[600],
                                  iconSize: 20,
                                  tooltip: 'Edit',
                                ),
                                IconButton(
                                  onPressed: () => _deleteRelationshipFromList(
                                      context, index),
                                  icon: const Icon(Icons.delete_outline),
                                  color: Colors.red[600],
                                  iconSize: 20,
                                  tooltip: 'Delete',
                                ),
                              ],
                            ),
                          );
                        }),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttributeConfigurationTable(BuildContext context,
      {ObjectCreationModel? objectData}) {
    return Container(
      padding: const EdgeInsets.all(6),
      margin: const EdgeInsets.symmetric(horizontal: 6),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and Add Attribute button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Attribute Configuration',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              IgnorePointer(
                ignoring: !Provider.of<WebHomeProviderStatic>(context).isAIMode,
                child: Opacity(
                  opacity: !Provider.of<WebHomeProviderStatic>(context).isAIMode
                      ? 0.5
                      : 1.0,
                  child: ElevatedButton.icon(
                    onPressed: () => _showAddAttributeModal(context),
                    icon: const Icon(Icons.add, size: 16),
                    label: const Text('Add Attribute'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0058FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 6),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                      elevation: 0,
                      textStyle: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Table container with scrollable data columns and fixed Actions column
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFE5E7EB)),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Row(
              children: [
                // Scrollable data columns section
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Column(
                      children: [
                        // Scrollable header
                        Container(
                          decoration: const BoxDecoration(
                            color: Color(0xFFF9FAFB),
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(6),
                            ),
                          ),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 8),
                          child: _buildScrollableAttributeTableHeader(
                              context, objectData),
                        ),
                        // Scrollable body
                        Container(
                          constraints: const BoxConstraints(maxHeight: 200),
                          child: SingleChildScrollView(
                            child: Column(
                              children: _getAttributeTableData(objectData)
                                  .asMap()
                                  .entries
                                  .map((entry) {
                                int index = entry.key;
                                Map<String, String> data = entry.value;
                                return _buildScrollableAttributeTableRow(
                                    context, index, data, objectData);
                              }).toList(),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Fixed Actions column (header + body)
                Container(
                  decoration: const BoxDecoration(
                    border: Border(
                      left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                    ),
                  ),
                  child: Column(
                    children: [
                      // Fixed Actions header
                      Container(
                        decoration: const BoxDecoration(
                          color: Color(0xFFF9FAFB),
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(6),
                          ),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 14, vertical: 8),
                        child: Text(
                          'ACTIONS',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.labelMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.grey[700],
                          ),
                        ),
                      ),
                      // Fixed Actions body
                      Container(
                        constraints: const BoxConstraints(maxHeight: 200),
                        child: SingleChildScrollView(
                          child: Column(
                            children: _getAttributeTableData(objectData)
                                .asMap()
                                .entries
                                .map((entry) {
                              int index = entry.key;
                              Map<String, String> data = entry.value;
                              return _buildFixedAttributeActionsTableRow(
                                  context, index, data);
                            }).toList(),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds header cell for relationship table
  Widget _buildHeaderCell(BuildContext context, String title, double width) {
    return Container(
      width: width,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Text(
        title,
        style: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.labelMedium(context),
          fontWeight: FontWeight.w600,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.grey[700],
        ),
      ),
    );
  }

  /// Builds data row for relationship table
  Widget _buildRelationshipDataRow(
      BuildContext context, int index, EntityRelationship relationship) {
    return Container(
      height: 50,
      decoration: BoxDecoration(
        color: Colors.white,
        border: index < _relationshipData.length - 1
            ? const Border(
                bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
              )
            : null,
      ),
      child: Row(
        children: [
          _buildDataCell(context, relationship.relatedEntity ?? '', 180),
          _buildDataCell(context, relationship.relationshipType ?? '', 150),
          _buildDataCell(context, relationship.foreignKey ?? '', 120),
          _buildDataCell(context, relationship.description ?? '', 200),
          _buildDataCell(context, 'Active', 100),
        ],
      ),
    );
  }

  /// Builds data cell for relationship table
  Widget _buildDataCell(BuildContext context, String text, double width) {
    return Container(
      width: width,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Text(
        text,
        style: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.bodyMedium(context),
          fontWeight: FontWeight.w400,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.black,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// Builds scrollable attribute table header (data columns only, no Actions)
  Widget _buildScrollableAttributeTableHeader(
      BuildContext context, ObjectCreationModel? objectData) {
    final headers = _getDynamicAttributeHeaders(objectData);
    final widths = _getDynamicColumnWidths();

    return Row(
      children: headers.map((header) {
        final width = widths[header] ?? 150.0;
        return Container(
          width: width,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Text(
            header,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.labelMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[700],
            ),
          ),
        );
      }).toList(),
    );
  }

  /// Builds fixed actions table row for attribute table
  Widget _buildFixedAttributeActionsTableRow(
    BuildContext context,
    int index,
    Map<String, String> data,
  ) {
    return Container(
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: IgnorePointer(
        ignoring: !Provider.of<WebHomeProviderStatic>(context).isAIMode,
        child: Opacity(
          opacity:
              !Provider.of<WebHomeProviderStatic>(context).isAIMode ? 0.5 : 1.0,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  onPressed: () {
                    _showEditAttributeModal(
                      context,
                      index,
                      data['name'] ?? data['attributeName'] ?? '',
                      data['displayName'] ?? '',
                      data['dataType'] ?? '',
                      data['required'] ?? '',
                      data['unique'] ?? '',
                    );
                  },
                  icon: const Icon(Icons.edit_outlined),
                  color: Colors.blue[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Edit',
                ),
                const SizedBox(width: 4),
                IconButton(
                  onPressed: () =>
                      _deleteAttribute(context, index, data['name'] ?? ''),
                  icon: const Icon(Icons.delete_outline),
                  color: Colors.red[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Delete',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAttributeDisplayRow(
    BuildContext context,
    int index,
    String attributeName,
    String displayName,
    String dataType,
    String required,
    String unique,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: [
          // Attribute Name
          SizedBox(
            width: 150,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Tooltip(
                message: attributeName,
                child: Text(
                  attributeName,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ),
          ),

          // Display Name
          SizedBox(
            width: 150,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Tooltip(
                message: displayName,
                child: Text(
                  displayName,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ),
          ),

          // Data Type
          SizedBox(
            width: 120,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Tooltip(
                message: dataType,
                child: Text(
                  dataType,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ),
          ),

          // Required
          SizedBox(
            width: 100,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Tooltip(
                message: required,
                child: Text(
                  required,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ),
          ),

          // Unique
          SizedBox(
            width: 100,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Tooltip(
                message: unique,
                child: Text(
                  unique,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ),
          ),

          // Actions
          SizedBox(
            width: 120,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                IconButton(
                  onPressed: () => _showEditAttributeModal(context, index,
                      attributeName, displayName, dataType, required, unique),
                  icon: const Icon(Icons.edit_outlined),
                  color: Colors.blue[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Edit',
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () =>
                      _deleteAttribute(context, index, attributeName),
                  icon: const Icon(Icons.delete_outline),
                  color: Colors.red[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Delete',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttributeDisplayRowWithFixedActions(
    BuildContext context,
    int index,
    String attributeName,
    String displayName,
    String dataType,
    String required,
    String unique,
  ) {
    return Container(
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: [
          // Scrollable data columns
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                child: Row(
                  children: [
                    // Attribute Name
                    SizedBox(
                      width: 150,
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Tooltip(
                          message: attributeName,
                          child: Text(
                            attributeName,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                    ),

                    // Display Name
                    SizedBox(
                      width: 150,
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Tooltip(
                          message: displayName,
                          child: Text(
                            displayName,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                    ),

                    // Data Type
                    SizedBox(
                      width: 120,
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Tooltip(
                          message: dataType,
                          child: Text(
                            dataType,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                    ),

                    // Required
                    SizedBox(
                      width: 100,
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Tooltip(
                          message: required,
                          child: Text(
                            required,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                    ),

                    // Unique
                    SizedBox(
                      width: 100,
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Tooltip(
                          message: unique,
                          child: Text(
                            unique,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Fixed Actions column
          Container(
            width: 80,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: const BoxDecoration(
              border: Border(
                left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  onPressed: () => _showEditAttributeModal(context, index,
                      attributeName, displayName, dataType, required, unique),
                  icon: const Icon(Icons.edit_outlined),
                  color: Colors.blue[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Edit',
                ),
                const SizedBox(width: 4),
                IconButton(
                  onPressed: () =>
                      _deleteAttribute(context, index, attributeName),
                  icon: const Icon(Icons.delete_outline),
                  color: Colors.red[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Delete',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttributeFormRow(
    BuildContext context,
    int index,
    String attributeName,
    String displayName,
    String dataType,
    String required,
    String unique,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Attribute Name Field
          Container(
            width: 150,
            alignment: Alignment.centerLeft,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            child: TextFormField(
              initialValue: attributeName,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: const BorderSide(color: Color(0xFF0058FF)),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                isDense: true,
              ),
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),

          // Display Name Field
          Container(
            width: 150,
            alignment: Alignment.centerLeft,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            child: TextFormField(
              initialValue: displayName,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: const BorderSide(color: Color(0xFF0058FF)),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                isDense: true,
              ),
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),

          // Data Type Dropdown
          Container(
            width: 120,
            alignment: Alignment.centerLeft,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            child: SizedBox(
              height: 33, // Fixed height to match TextFormField
              child: DropdownButtonFormField<String>(
                value: dataType,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: const BorderSide(color: Color(0xFF0058FF)),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  isDense: true,
                ),
                icon: Container(
                  alignment: Alignment.centerRight,
                  child: const Icon(
                    Icons.keyboard_arrow_down,
                    size: 20,
                  ),
                ),
                iconSize: 20,
                items:
                    ['string', 'number', 'boolean', 'date'].map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(
                      value,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodySmall(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  // Handle change
                },
              ),
            ),
          ),

          // Required Dropdown
          Container(
            width: 100,
            alignment: Alignment.centerLeft,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            child: SizedBox(
              height: 33, // Fixed height to match TextFormField
              child: DropdownButtonFormField<String>(
                value: required,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: const BorderSide(color: Color(0xFF0058FF)),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  isDense: true,
                ),
                icon: Container(
                  alignment: Alignment.centerRight,
                  child: const Icon(
                    Icons.keyboard_arrow_down,
                    size: 20,
                  ),
                ),
                iconSize: 20,
                items: ['YES', 'NO'].map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(
                      value,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodySmall(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  // Handle change
                },
              ),
            ),
          ),

          // Unique Dropdown
          Container(
            width: 100,
            alignment: Alignment.centerLeft,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            child: SizedBox(
              height: 33, // Fixed height to match TextFormField
              child: DropdownButtonFormField<String>(
                value: unique,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: const BorderSide(color: Color(0xFF0058FF)),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  isDense: true,
                ),
                icon: Container(
                  alignment: Alignment.centerRight,
                  child: const Icon(
                    Icons.keyboard_arrow_down,
                    size: 20,
                  ),
                ),
                iconSize: 20,
                items: ['YES', 'NO'].map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(
                      value,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodySmall(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  // Handle change
                },
              ),
            ),
          ),

          // Delete Action
          Container(
            width: 80,
            alignment: Alignment.center,
            child: IconButton(
              onPressed: () {
                // Handle delete
              },
              icon: const Icon(Icons.delete_outline),
              color: Colors.red[600],
              iconSize: 20,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
          ),
        ],
      ),
    );
  }

  void _showAddAttributeModal(BuildContext context) {
    _showEnhancedAttributeDialog(
      context: context,
      title: 'Attribute Configuration',
      isEditMode: false,
    );
  }

  void _showAddEntityRelationshipModal(BuildContext context) {
    _showEnhancedRelationshipDialog(
      context: context,
      title: 'Add Entity Relationship',
      isEditMode: false,
    );
  }

  void _showAddBusinessRuleModal(BuildContext context) {
    _showEnhancedBusinessRuleDialog(
      context: context,
      title: 'Add Business Rule',
      isEditMode: false,
    );
  }

  /// Show enhanced security classification dialog with form validation and right panel
  void _showEnhancedSecurityClassificationDialog({
    required BuildContext context,
    required String title,
    required bool isEditMode,
    SecurityClassification? editData,
    int? editIndex,
  }) {
    // Initialize controllers with edit data if available
    final entityAttributeController = TextEditingController(text: editData?.entityAttribute ?? '');
    final maskingPatternController = TextEditingController(text: editData?.maskingPattern ?? '');
    final retentionOverrideController = TextEditingController(text: editData?.retentionOverride ?? '');

    String selectedClassification = editData?.classification ?? 'public';
    String selectedPiiType = editData?.piiType ?? 'none';
    String selectedEncryptionType = editData?.encryptionType ?? 'none';
    String selectedMaskingRequired = editData?.maskingRequired == true ? 'Yes' : 'No';
    String selectedAccessLevel = editData?.accessLevel ?? 'read_public';
    String selectedAuditTrail = editData?.auditTrail == true ? 'Yes' : 'No';
    String selectedDataResidency = editData?.dataResidency ?? 'global';
    String selectedAnonymizationRequired = editData?.anonymizationRequired ?? 'No';
    String selectedAnonymizationMethod = editData?.anonymizationMethod ?? 'hash';
    String selectedComplianceFrameworks = editData?.complianceFrameworks?.isNotEmpty == true
        ? editData!.complianceFrameworks!.first.toString() : 'gdpr';

    // Show security classification list if there are existing items, otherwise hide it
    _showSecurityClassificationList = _securityClassificationData.isNotEmpty;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            // Check if all required fields are filled
            bool isFormValid = entityAttributeController.text.isNotEmpty &&
                selectedClassification.isNotEmpty &&
                selectedPiiType.isNotEmpty &&
                selectedEncryptionType.isNotEmpty &&
                selectedMaskingRequired.isNotEmpty &&
                selectedAccessLevel.isNotEmpty &&
                selectedAuditTrail.isNotEmpty &&
                selectedDataResidency.isNotEmpty &&
                selectedAnonymizationRequired.isNotEmpty &&
                selectedAnonymizationMethod.isNotEmpty &&
                selectedComplianceFrameworks.isNotEmpty;

            return Dialog(
              backgroundColor: Colors.transparent,
              child: Container(
                width: 780,
                constraints: const BoxConstraints(maxHeight: 600),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: const BoxDecoration(
                        border: Border(
                          bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                        ),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              title,
                              style: FontManager.getCustomStyle(
                                fontSize: ResponsiveFontSizes.headlineSmall(context),
                                fontWeight: FontWeight.w600,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                          ),
                          Row(
                            children: [
                              if (!isEditMode)
                                ElevatedButton(
                                  onPressed: isFormValid ? () {
                                    // Save the current form data as SecurityClassification object
                                    final securityClassification = SecurityClassification(
                                      entityAttribute: entityAttributeController.text,
                                      classification: selectedClassification,
                                      piiType: selectedPiiType,
                                      encryptionRequired: selectedEncryptionType != 'none',
                                      encryptionType: selectedEncryptionType,
                                      maskingRequired: selectedMaskingRequired == 'Yes',
                                      maskingPattern: maskingPatternController.text.isNotEmpty ? maskingPatternController.text : null,
                                      accessLevel: selectedAccessLevel,
                                      auditTrail: selectedAuditTrail == 'Yes',
                                      dataResidency: selectedDataResidency,
                                      retentionOverride: retentionOverrideController.text.isNotEmpty ? retentionOverrideController.text : null,
                                      anonymizationRequired: selectedAnonymizationRequired,
                                      anonymizationMethod: selectedAnonymizationMethod,
                                      complianceFrameworks: [selectedComplianceFrameworks],
                                    );

                                    setState(() {
                                      if (isEditMode && editIndex != null) {
                                        // Update existing item
                                        _securityClassificationData[editIndex] = securityClassification;
                                      } else {
                                        // Add new item
                                        _securityClassificationData.add(securityClassification);
                                      }
                                      _showSecurityClassificationList = true;

                                      // Clear form fields
                                      entityAttributeController.clear();
                                      maskingPatternController.clear();
                                      retentionOverrideController.clear();
                                      selectedClassification = 'public';
                                      selectedPiiType = 'none';
                                      selectedEncryptionType = 'none';
                                      selectedMaskingRequired = 'No';
                                      selectedAccessLevel = 'read_public';
                                      selectedAuditTrail = 'No';
                                      selectedDataResidency = 'global';
                                      selectedAnonymizationRequired = 'No';
                                      selectedAnonymizationMethod = 'hash';
                                      selectedComplianceFrameworks = 'gdpr';
                                    });
                                  } : null,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: isFormValid ? const Color(0xFF0058FF) : Colors.grey[300],
                                    foregroundColor: isFormValid ? Colors.white : Colors.grey[600],
                                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    elevation: 0,
                                  ),
                                  child: Text(
                                    'Add more Security',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodySmall(context),
                                      fontWeight: FontWeight.w500,
                                      fontFamily: FontManager.fontFamilyTiemposText,
                                      color: isFormValid ? Colors.white : Colors.grey[600],
                                    ),
                                  ),
                                ),
                              const SizedBox(width: 8),
                              IconButton(
                                onPressed: () => Navigator.of(context).pop(),
                                icon: const Icon(Icons.close),
                                iconSize: 24,
                                color: Colors.grey[600],
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    // Main content area
                    Expanded(
                      child: Row(
                        children: [
                          // Left side - Form (70% always)
                          Expanded(
                            flex: 7,
                            child: SingleChildScrollView(
                              padding: const EdgeInsets.all(24),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Left column
                                  Expanded(
                                    child: Column(
                                      children: [
                                        _buildEditableFormField(context, 'Entity.Attribute', entityAttributeController),
                                        const SizedBox(height: 20),
                                        _buildEditableDropdownField(context, 'PII Type',
                                            ['none', 'name', 'email', 'phone', 'medical', 'financial', 'ssn', 'national_id', 'employee_id', 'username', 'address', 'postal_code', 'date_of_birth', 'age', 'gender', 'marital_status', 'emergency_contact', 'biometric', 'genetic', 'religious', 'political', 'sexual_orientation', 'criminal', 'ip_address', 'device_id', 'location', 'cookies', 'login_credentials', 'job_title', 'department', 'performance', 'education', 'certification', 'family_member', 'dependent', 'beneficiary', 'sensitive_personal', 'government_id', 'immigration', 'union_membership'], selectedPiiType, (value) {
                                          setState(() {
                                            selectedPiiType = value!;
                                          });
                                        }),
                                        const SizedBox(height: 20),
                                        _buildEditableDropdownField(context, 'Masking Required',
                                            ['Yes', 'No'], selectedMaskingRequired, (value) {
                                          setState(() {
                                            selectedMaskingRequired = value!;
                                          });
                                        }),
                                        const SizedBox(height: 20),
                                        _buildEditableDropdownField(context, 'Access Level',
                                            ['read_public', 'read_internal', 'read_restricted'], selectedAccessLevel, (value) {
                                          setState(() {
                                            selectedAccessLevel = value!;
                                          });
                                        }),
                                        const SizedBox(height: 20),
                                        _buildEditableDropdownField(context, 'Data Residency',
                                            ['global', 'regional', 'local'], selectedDataResidency, (value) {
                                          setState(() {
                                            selectedDataResidency = value!;
                                          });
                                        }),
                                        const SizedBox(height: 20),
                                        _buildEditableDropdownField(context, 'Anonymization Required',
                                            ['Yes', 'No'], selectedAnonymizationRequired, (value) {
                                          setState(() {
                                            selectedAnonymizationRequired = value!;
                                          });
                                        }),
                                        const SizedBox(height: 20),
                                        _buildEditableDropdownField(context, 'Compliance Frameworks',
                                            ['gdpr', 'hipaa', 'sox'], selectedComplianceFrameworks, (value) {
                                          setState(() {
                                            selectedComplianceFrameworks = value!;
                                          });
                                        }),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 24),
                                  // Right column
                                  Expanded(
                                    child: Column(
                                      children: [
                                        _buildEditableDropdownField(context, 'Classification',
                                            ['public', 'internal', 'confidential'], selectedClassification, (value) {
                                          setState(() {
                                            selectedClassification = value!;
                                          });
                                        }),
                                        const SizedBox(height: 20),
                                        _buildEditableDropdownField(context, 'Encryption Type',
                                            ['none', 'aes128', 'aes192', 'aes256', 'aes128_gcm', 'aes192_gcm', 'aes256_gcm', 'aes128_cbc', 'aes192_cbc', 'aes256_cbc', 'aes128_ecb', 'aes192_ecb', 'aes256_ecb', 'aes128_cfb', 'aes192_cfb', 'aes256_cfb', 'aes128_ofb', 'aes192_ofb', 'aes256_ofb', 'des', '3des', 'blowfish', 'twofish', 'serpent', 'chacha20', 'chacha20_poly1305', 'salsa20', 'rc4', 'rc5', 'rc6', 'rsa1024', 'rsa2048', 'rsa3072', 'rsa4096', 'ecc256', 'ecc384', 'ecc521', 'ecdsa256', 'ecdsa384', 'ecdsa521', 'ed25519', 'curve25519', 'dh1024', 'dh2048', 'dh3072', 'dh4096', 'sha1', 'sha224', 'sha256', 'sha384', 'sha512', 'sha3_224', 'sha3_256', 'sha3_384', 'sha3_512', 'md5', 'blake2b', 'blake2s', 'bcrypt', 'scrypt', 'argon2', 'pbkdf2', 'hmac_sha256', 'hmac_sha512', 'format_preserving', 'tokenization', 'pseudonymization', 'homomorphic', 'searchable', 'order_preserving'], selectedEncryptionType, (value) {
                                          setState(() {
                                            selectedEncryptionType = value!;
                                          });
                                        }),
                                        const SizedBox(height: 20),
                                        _buildEditableFormField(context, 'Masking Pattern', maskingPatternController),
                                        const SizedBox(height: 20),
                                        _buildEditableDropdownField(context, 'Audit Trail',
                                            ['Yes', 'No'], selectedAuditTrail, (value) {
                                          setState(() {
                                            selectedAuditTrail = value!;
                                          });
                                        }),
                                        const SizedBox(height: 20),
                                        _buildEditableFormField(context, 'Retention Override', retentionOverrideController),
                                        const SizedBox(height: 20),
                                        _buildEditableDropdownField(context, 'Anonymization Method',
                                            ['hash', 'tokenize', 'pseudonymize', 'remove'], selectedAnonymizationMethod, (value) {
                                          setState(() {
                                            selectedAnonymizationMethod = value!;
                                          });
                                        }),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          // Right side - Always show (30% space)
                          Expanded(
                            flex: 3,
                            child: Container(
                              decoration: const BoxDecoration(
                                border: Border(
                                  left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                                ),
                                color: Colors.white,
                              ),
                              child: _showSecurityClassificationList
                                  ? _buildSecurityClassificationListPanel()
                                  : Container(
                                      width: double.infinity,
                                      height: double.infinity,
                                      color: Colors.white,
                                    ), // Empty white space when list is not shown
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Footer
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: const BoxDecoration(
                        border: Border(
                          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              'Cancel',
                              style: FontManager.getCustomStyle(
                                fontSize: ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.grey[600],
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          ElevatedButton(
                            onPressed: () {
                              // For Validate button, just close the dialog
                              Navigator.of(context).pop();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF0058FF),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              elevation: 0,
                            ),
                            child: Text(
                              'Validate',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// Show enhanced system permissions dialog with form validation and right panel
  void _showEnhancedSystemPermissionsDialog({
    required BuildContext context,
    required String title,
    required bool isEditMode,
    SystemPermission? editData,
    int? editIndex,
  }) {
    // Initialize controllers with edit data if available
    final permissionIdController = TextEditingController(text: editData?.permissionId ?? '');
    final permissionNameController = TextEditingController(text: editData?.permissionName ?? '');
    final resourceIdentifierController = TextEditingController(text: editData?.resourceIdentifier ?? '');
    final descriptionController = TextEditingController(text: editData?.description ?? '');
    final naturalLanguageController = TextEditingController(text: editData?.naturalLanguage ?? '');
    final versionController = TextEditingController(text: editData?.version?.toString() ?? '1');

    String selectedPermissionType = editData?.permissionType ?? 'entity';
    String selectedScope = editData?.scope ?? 'own_records';
    String selectedStatus = editData?.status ?? 'active';

    // Actions checkboxes - parse from actions list
    bool createAction = editData?.actions?.contains('Create') ?? false;
    bool readAction = editData?.actions?.contains('Read') ?? false;
    bool updateAction = editData?.actions?.contains('Update') ?? false;
    bool deleteAction = editData?.actions?.contains('Delete') ?? false;

    // Show system permissions list if there are existing items, otherwise hide it
    _showSystemPermissionsList = _systemPermissionData.isNotEmpty;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            // Check if all required fields are filled
            bool isFormValid = permissionIdController.text.isNotEmpty &&
                permissionNameController.text.isNotEmpty &&
                selectedPermissionType.isNotEmpty &&
                resourceIdentifierController.text.isNotEmpty &&
                selectedScope.isNotEmpty &&
                descriptionController.text.isNotEmpty &&
                selectedStatus.isNotEmpty &&
                naturalLanguageController.text.isNotEmpty &&
                versionController.text.isNotEmpty;

            return Dialog(
              backgroundColor: Colors.transparent,
              child: Container(
                width: 780,
                constraints: const BoxConstraints(maxHeight: 600),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: const BoxDecoration(
                        border: Border(
                          bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                        ),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              title,
                              style: FontManager.getCustomStyle(
                                fontSize: ResponsiveFontSizes.headlineSmall(context),
                                fontWeight: FontWeight.w600,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                          ),
                          Row(
                            children: [
                              if (!isEditMode)
                                ElevatedButton(
                                  onPressed: isFormValid ? () {
                                    // Build actions list from checkboxes
                                    List<String> actions = [];
                                    if (createAction) actions.add('Create');
                                    if (readAction) actions.add('Read');
                                    if (updateAction) actions.add('Update');
                                    if (deleteAction) actions.add('Delete');

                                    // Save the current form data as SystemPermission object
                                    final systemPermission = SystemPermission(
                                      permissionId: permissionIdController.text,
                                      permissionName: permissionNameController.text,
                                      permissionType: selectedPermissionType,
                                      resourceIdentifier: resourceIdentifierController.text,
                                      actions: actions,
                                      description: descriptionController.text,
                                      scope: selectedScope,
                                      naturalLanguage: naturalLanguageController.text,
                                      version: int.tryParse(versionController.text) ?? 1,
                                      status: selectedStatus,
                                    );

                                    setState(() {
                                      if (isEditMode && editIndex != null) {
                                        // Update existing item
                                        _systemPermissionData[editIndex] = systemPermission;
                                      } else {
                                        // Add new item
                                        _systemPermissionData.add(systemPermission);
                                      }
                                      _showSystemPermissionsList = true;

                                      // Clear form fields
                                      permissionIdController.clear();
                                      permissionNameController.clear();
                                      resourceIdentifierController.clear();
                                      descriptionController.clear();
                                      naturalLanguageController.clear();
                                      versionController.text = '1';
                                      selectedPermissionType = 'entity';
                                      selectedScope = 'own_records';
                                      selectedStatus = 'active';
                                      createAction = false;
                                      readAction = false;
                                      updateAction = false;
                                      deleteAction = false;
                                    });
                                  } : null,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: isFormValid ? const Color(0xFF0058FF) : Colors.grey[300],
                                    foregroundColor: isFormValid ? Colors.white : Colors.grey[600],
                                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    elevation: 0,
                                  ),
                                  child: Text(
                                    'Add More Permissions',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodySmall(context),
                                      fontWeight: FontWeight.w500,
                                      fontFamily: FontManager.fontFamilyTiemposText,
                                      color: isFormValid ? Colors.white : Colors.grey[600],
                                    ),
                                  ),
                                ),
                              const SizedBox(width: 8),
                              IconButton(
                                onPressed: () => Navigator.of(context).pop(),
                                icon: const Icon(Icons.close),
                                iconSize: 24,
                                color: Colors.grey[600],
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    // Main content area
                    Expanded(
                      child: Row(
                        children: [
                          // Left side - Form (70% always)
                          Expanded(
                            flex: 7,
                            child: SingleChildScrollView(
                              padding: const EdgeInsets.all(24),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Left column
                                  Expanded(
                                    child: Column(
                                      children: [
                                        _buildEditableFormField(context, 'Permission ID', permissionIdController),
                                        const SizedBox(height: 20),
                                        _buildEditableDropdownField(context, 'Permission Type',
                                            ['entity', 'attribute', 'operation'], selectedPermissionType, (value) {
                                          setState(() {
                                            selectedPermissionType = value!;
                                          });
                                        }),
                                        const SizedBox(height: 20),
                                        // Actions checkboxes
                                        Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Actions',
                                              style: FontManager.getCustomStyle(
                                                fontSize: ResponsiveFontSizes.bodyMedium(context),
                                                fontWeight: FontWeight.w500,
                                                fontFamily: FontManager.fontFamilyTiemposText,
                                                color: Colors.black,
                                              ),
                                            ),
                                            const SizedBox(height: 8),
                                            Wrap(
                                              spacing: 8,
                                              runSpacing: 8,
                                              children: [
                                                _buildActionButton('Create', createAction, (value) {
                                                  setState(() {
                                                    createAction = value;
                                                  });
                                                }),
                                                _buildActionButton('Read', readAction, (value) {
                                                  setState(() {
                                                    readAction = value;
                                                  });
                                                }),
                                                _buildActionButton('Update', updateAction, (value) {
                                                  setState(() {
                                                    updateAction = value;
                                                  });
                                                }),
                                                _buildActionButton('Delete', deleteAction, (value) {
                                                  setState(() {
                                                    deleteAction = value;
                                                  });
                                                }),
                                              ],
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 20),
                                        _buildEditableFormField(context, 'Description', descriptionController),
                                        const SizedBox(height: 20),
                                        _buildEditableFormField(context, 'Natural Language', naturalLanguageController),
                                        const SizedBox(height: 20),
                                        _buildEditableFormField(context, 'Version', versionController),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 24),
                                  // Right column
                                  Expanded(
                                    child: Column(
                                      children: [
                                        _buildEditableFormField(context, 'Permission Name', permissionNameController),
                                        const SizedBox(height: 20),
                                        _buildEditableFormField(context, 'Resource Identifier', resourceIdentifierController),
                                        const SizedBox(height: 20),
                                        _buildEditableDropdownField(context, 'Scope',
                                            ['own_records', 'department_records', 'tenant_records'], selectedScope, (value) {
                                          setState(() {
                                            selectedScope = value!;
                                          });
                                        }),
                                        const SizedBox(height: 20),
                                        _buildEditableDropdownField(context, 'Status',
                                            ['active', 'inactive'], selectedStatus, (value) {
                                          setState(() {
                                            selectedStatus = value!;
                                          });
                                        }),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          // Right side - Always show (30% space)
                          Expanded(
                            flex: 3,
                            child: Container(
                              decoration: const BoxDecoration(
                                border: Border(
                                  left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                                ),
                                color: Colors.white,
                              ),
                              child: _showSystemPermissionsList
                                  ? _buildSystemPermissionsListPanel()
                                  : Container(
                                      width: double.infinity,
                                      height: double.infinity,
                                      color: Colors.white,
                                    ), // Empty white space when list is not shown
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Footer
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: const BoxDecoration(
                        border: Border(
                          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              'Cancel',
                              style: FontManager.getCustomStyle(
                                fontSize: ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.grey[600],
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          ElevatedButton(
                            onPressed: () {
                              // For Apply This button, just close the dialog
                              Navigator.of(context).pop();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF0058FF),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              elevation: 0,
                            ),
                            child: Text(
                              'Apply This',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// Show enhanced enumerated values dialog with form validation and right panel
  void _showEnhancedEnumeratedValuesDialog({
    required BuildContext context,
    required String title,
    required bool isEditMode,
    EnumValue? editData,
    int? editIndex,
  }) {
    // Initialize controllers with edit data if available
    final entityAttributeController = TextEditingController(text: editData?.entityAttribute ?? '');
    final enumNameController = TextEditingController(text: editData?.enumName ?? '');
    final valueController = TextEditingController(text: editData?.value ?? '');
    final displayController = TextEditingController(text: editData?.display ?? '');
    final descriptionController = TextEditingController(text: editData?.description ?? '');
    final sortOrderController = TextEditingController(text: editData?.sortOrder?.toString() ?? '');

    String selectedActive = editData?.active == true ? 'Yes' : 'No';

    // Show enumerated values list if there are existing items, otherwise hide it
    _showEnumValuesList = _enumValueData.isNotEmpty;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            // Check if all required fields are filled
            bool isFormValid = entityAttributeController.text.isNotEmpty &&
                enumNameController.text.isNotEmpty &&
                valueController.text.isNotEmpty &&
                displayController.text.isNotEmpty &&
                descriptionController.text.isNotEmpty &&
                sortOrderController.text.isNotEmpty &&
                selectedActive.isNotEmpty;

            return Dialog(
              backgroundColor: Colors.transparent,
              child: Container(
                width: 780,
                constraints: const BoxConstraints(maxHeight: 600),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: const BoxDecoration(
                        border: Border(
                          bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                        ),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              title,
                              style: FontManager.getCustomStyle(
                                fontSize: ResponsiveFontSizes.headlineSmall(context),
                                fontWeight: FontWeight.w600,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                          ),
                          Row(
                            children: [
                              if (!isEditMode)
                                ElevatedButton(
                                  onPressed: isFormValid ? () {
                                    // Save the current form data as EnumValue object
                                    final enumValue = EnumValue(
                                      entityAttribute: entityAttributeController.text,
                                      enumName: enumNameController.text,
                                      value: valueController.text,
                                      display: displayController.text,
                                      description: descriptionController.text,
                                      sortOrder: int.tryParse(sortOrderController.text),
                                      active: selectedActive == 'Yes',
                                    );

                                    setState(() {
                                      if (isEditMode && editIndex != null) {
                                        // Update existing item
                                        _enumValueData[editIndex] = enumValue;
                                      } else {
                                        // Add new item
                                        _enumValueData.add(enumValue);
                                      }
                                      _showEnumValuesList = true;

                                      // Clear form fields
                                      entityAttributeController.clear();
                                      enumNameController.clear();
                                      valueController.clear();
                                      displayController.clear();
                                      descriptionController.clear();
                                      sortOrderController.clear();
                                      selectedActive = 'Yes';
                                    });
                                  } : null,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: isFormValid ? const Color(0xFF0058FF) : Colors.grey[300],
                                    foregroundColor: isFormValid ? Colors.white : Colors.grey[600],
                                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    elevation: 0,
                                  ),
                                  child: Text(
                                    'Add Enumerated',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodySmall(context),
                                      fontWeight: FontWeight.w500,
                                      fontFamily: FontManager.fontFamilyTiemposText,
                                      color: isFormValid ? Colors.white : Colors.grey[600],
                                    ),
                                  ),
                                ),
                              const SizedBox(width: 8),
                              IconButton(
                                onPressed: () => Navigator.of(context).pop(),
                                icon: const Icon(Icons.close),
                                iconSize: 24,
                                color: Colors.grey[600],
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    // Main content area
                    Expanded(
                      child: Row(
                        children: [
                          // Left side - Form (70% always)
                          Expanded(
                            flex: 7,
                            child: SingleChildScrollView(
                              padding: const EdgeInsets.all(24),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Left column
                                  Expanded(
                                    child: Column(
                                      children: [
                                        _buildEditableFormField(context, 'Entity.Attribute', entityAttributeController),
                                        const SizedBox(height: 20),
                                        _buildEditableFormField(context, 'Value', valueController),
                                        const SizedBox(height: 20),
                                        _buildEditableFormField(context, 'Description', descriptionController),
                                        const SizedBox(height: 20),
                                        _buildEditableDropdownField(context, 'Active',
                                            ['Yes', 'No'], selectedActive, (value) {
                                          setState(() {
                                            selectedActive = value!;
                                          });
                                        }),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 24),
                                  // Right column
                                  Expanded(
                                    child: Column(
                                      children: [
                                        _buildEditableFormField(context, 'Enum Name', enumNameController),
                                        const SizedBox(height: 20),
                                        _buildEditableFormField(context, 'Display', displayController),
                                        const SizedBox(height: 20),
                                        _buildEditableFormField(context, 'Sort Order', sortOrderController),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          // Right side - Always show (30% space)
                          Expanded(
                            flex: 3,
                            child: Container(
                              decoration: const BoxDecoration(
                                border: Border(
                                  left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                                ),
                                color: Colors.white,
                              ),
                              child: _showEnumValuesList
                                  ? _buildEnumeratedValuesListPanel()
                                  : Container(
                                      width: double.infinity,
                                      height: double.infinity,
                                      color: Colors.white,
                                    ), // Empty white space when list is not shown
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Footer
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: const BoxDecoration(
                        border: Border(
                          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              'Cancel',
                              style: FontManager.getCustomStyle(
                                fontSize: ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.grey[600],
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          ElevatedButton(
                            onPressed: () {
                              // For Validate button, just close the dialog
                              Navigator.of(context).pop();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF0058FF),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              elevation: 0,
                            ),
                            child: Text(
                              'Validate',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// Show enhanced relationship dialog with form validation and right panel
  void _showEnhancedRelationshipDialog({
    required BuildContext context,
    required String title,
    required bool isEditMode,
    Map<String, dynamic>? editData,
    int? editIndex,
  }) {
    // Initialize controllers with edit data if available
    final descriptionController =
        TextEditingController(text: editData?['description'] ?? '');

    String selectedPrimaryKey = editData?['primaryKey'] ?? 'Select Attribute';
    String selectedForeignKey = editData?['foreignKey'] ?? 'Select Attribute';
    String selectedRelatedEntity = editData?['relatedEntity'] ?? 'Customer';
    String selectedRelationshipType =
        editData?['relationshipType'] ?? 'one-to-one';
    String selectedOnDelete = editData?['onDelete'] ?? 'Restrict';
    String selectedOnUpdate = editData?['onUpdate'] ?? 'Restrict';
    String selectedForeignKeyType = editData?['foreignKeyType'] ?? 'Nullable';

    // Show relationship list if there are existing relationships, otherwise hide it
    _showRelationshipList = _relationshipData.isNotEmpty;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            // Check if all required fields are filled
            bool isFormValid = selectedPrimaryKey != 'Select Attribute' &&
                selectedForeignKey != 'Select Attribute' &&
                selectedRelatedEntity.isNotEmpty &&
                selectedRelationshipType.isNotEmpty &&
                selectedOnDelete.isNotEmpty &&
                selectedOnUpdate.isNotEmpty &&
                selectedForeignKeyType.isNotEmpty &&
                descriptionController.text.trim().isNotEmpty;

            // Add listeners to update validation state
            void updateValidation() {
              setState(() {});
            }

            // Add listeners for real-time validation
            descriptionController.removeListener(updateValidation);
            descriptionController.addListener(updateValidation);

            return Dialog(
              backgroundColor: Colors.transparent,
              child: Container(
                width: 780,
                constraints: const BoxConstraints(maxHeight: 600),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: const BoxDecoration(
                        border: Border(
                          bottom:
                              BorderSide(color: Color(0xFFE5E7EB), width: 1),
                        ),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              title,
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.headlineSmall(context),
                                fontWeight: FontWeight.w600,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                          ),
                          Row(
                            children: [
                              if (!isEditMode)
                                ElevatedButton(
                                  onPressed: isFormValid
                                      ? () {
                                          // Save the current form data
                                          final relationshipData =
                                              EntityRelationship(
                                            primaryKey: selectedPrimaryKey,
                                            foreignKey: selectedForeignKey,
                                            relatedEntity:
                                                selectedRelatedEntity,
                                            relationshipType:
                                                selectedRelationshipType,
                                            onDelete: selectedOnDelete,
                                            onUpdate: selectedOnUpdate,
                                            foreignKeyType:
                                                selectedForeignKeyType,
                                            description:
                                                descriptionController.text,
                                          );

                                          setState(() {
                                            _relationshipData
                                                .add(relationshipData);
                                            _showRelationshipList = true;

                                            // Clear form for next entry
                                            selectedPrimaryKey =
                                                'Select Attribute';
                                            selectedForeignKey =
                                                'Select Attribute';
                                            selectedRelatedEntity = 'Customer';
                                            descriptionController.clear();
                                            selectedRelationshipType =
                                                'one-to-one';
                                            selectedOnDelete = 'Restrict';
                                            selectedOnUpdate = 'Restrict';
                                            selectedForeignKeyType = 'Nullable';
                                          });
                                        }
                                      : null,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: isFormValid
                                        ? const Color(0xFF0058FF)
                                        : Colors.grey[400],
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 16, vertical: 8),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    elevation: 0,
                                  ),
                                  child: Text(
                                    'Add More',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodySmall(
                                          context),
                                      fontWeight: FontWeight.w500,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              const SizedBox(width: 8),
                              IconButton(
                                onPressed: () => Navigator.of(context).pop(),
                                icon: const Icon(Icons.close),
                                iconSize: 24,
                                color: Colors.grey[600],
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    // Main content area
                    Expanded(
                      child: Row(
                        children: [
                          // Left side - Form (70% always)
                          Expanded(
                            flex: 7,
                            child: SingleChildScrollView(
                              padding: const EdgeInsets.all(24),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Left column
                                  Expanded(
                                    child: Column(
                                      children: [
                                        _buildEditableDropdownField(
                                            context,
                                            'Primary key',
                                            [
                                              'Select Attribute',
                                              'id',
                                              'user_id',
                                              'customer_id',
                                              'order_id'
                                            ],
                                            selectedPrimaryKey, (value) {
                                          setState(() {
                                            selectedPrimaryKey = value!;
                                          });
                                        }),
                                        const SizedBox(height: 20),
                                        _buildEditableDropdownField(
                                            context,
                                            'Relationship Type',
                                            [
                                              'one-to-one',
                                              'one-to-many',
                                              'many-to-one',
                                              'many-to-many'
                                            ],
                                            selectedRelationshipType, (value) {
                                          setState(() {
                                            selectedRelationshipType = value!;
                                          });
                                        }),
                                        const SizedBox(height: 20),
                                        _buildEditableDropdownField(
                                            context,
                                            'On Delete',
                                            ['Restrict', 'Cascade', 'Set Null'],
                                            selectedOnDelete, (value) {
                                          setState(() {
                                            selectedOnDelete = value!;
                                          });
                                        }),
                                        const SizedBox(height: 20),
                                        _buildEditableFormField(
                                            context,
                                            'Description',
                                            descriptionController),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 24),
                                  // Right column
                                  Expanded(
                                    child: Column(
                                      children: [
                                        _buildEditableDropdownField(
                                            context,
                                            'Foreign key',
                                            [
                                              'Select Attribute',
                                              'user_id',
                                              'customer_id',
                                              'order_id',
                                              'product_id'
                                            ],
                                            selectedForeignKey, (value) {
                                          setState(() {
                                            selectedForeignKey = value!;
                                          });
                                        }),
                                        const SizedBox(height: 20),
                                        _buildEditableDropdownField(
                                            context,
                                            'Related Entity',
                                            ['Customer'],
                                            selectedRelatedEntity, (value) {
                                          setState(() {
                                            selectedRelatedEntity = value!;
                                          });
                                        }),
                                        const SizedBox(height: 20),
                                        _buildEditableDropdownField(
                                            context,
                                            'On Update',
                                            ['Restrict', 'Cascade', 'Set Null'],
                                            selectedOnUpdate, (value) {
                                          setState(() {
                                            selectedOnUpdate = value!;
                                          });
                                        }),
                                        const SizedBox(height: 20),
                                        _buildEditableDropdownField(
                                            context,
                                            'Foreign Key Type',
                                            ['Nullable', 'Non-Nullable'],
                                            selectedForeignKeyType, (value) {
                                          setState(() {
                                            selectedForeignKeyType = value!;
                                          });
                                        }),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          // Right side - Always show (30% space)
                          Expanded(
                            flex: 3,
                            child: Container(
                              decoration: const BoxDecoration(
                                border: Border(
                                  left: BorderSide(
                                      color: Color(0xFFE5E7EB), width: 1),
                                ),
                                color: Colors.white,
                              ),
                              child: _showRelationshipList
                                  ? _buildRelationshipListPanel()
                                  : Container(
                                      width: double.infinity,
                                      height: double.infinity,
                                      color: Colors.white,
                                    ), // Empty white space when list is not shown
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Footer
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: const BoxDecoration(
                        border: Border(
                          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              'Cancel',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.grey[600],
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          ElevatedButton(
                            onPressed: () {
                              if (isEditMode && editIndex != null) {
                                // Update existing relationship
                                final updatedRelationship = EntityRelationship(
                                  primaryKey: selectedPrimaryKey,
                                  foreignKey: selectedForeignKey,
                                  relatedEntity: selectedRelatedEntity,
                                  relationshipType: selectedRelationshipType,
                                  onDelete: selectedOnDelete,
                                  onUpdate: selectedOnUpdate,
                                  foreignKeyType: selectedForeignKeyType,
                                  description: descriptionController.text,
                                );

                                setState(() {
                                  _relationshipData[editIndex] =
                                      updatedRelationship;
                                });
                              }
                              Navigator.of(context).pop();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF0058FF),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              elevation: 0,
                            ),
                            child: Text(
                              isEditMode ? 'Update' : 'Apply Relationship',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// Show enhanced business rule dialog with form validation and right panel
  void _showEnhancedBusinessRuleDialog({
    required BuildContext context,
    required String title,
    required bool isEditMode,
    Map<String, dynamic>? editData,
    int? editIndex,
  }) {
    // Initialize controllers with edit data if available
    final attributeNameController =
        TextEditingController(text: editData?['attributeName'] ?? '');
    final leftOperandController =
        TextEditingController(text: editData?['leftOperand'] ?? '');
    final rightOperandController =
        TextEditingController(text: editData?['rightOperand'] ?? '');
    final successValueRangeController =
        TextEditingController(text: editData?['successValueRange'] ?? '');
    final warningValueRangeController =
        TextEditingController(text: editData?['warningValueRange'] ?? '');
    final failureValueRangeController =
        TextEditingController(text: editData?['failureValueRange'] ?? '');
    final successMessageController =
        TextEditingController(text: editData?['successMessage'] ?? '');
    final warningMessageController =
        TextEditingController(text: editData?['warningMessage'] ?? '');

    String selectedOperator = editData?['operator'] ?? 'IS_UNIQUE';
    String selectedMultiConditionOperator =
        editData?['multiConditionOperator'] ?? 'AND';

    // Show business rule list if there are existing business rules, otherwise hide it
    _showBusinessRuleList = _businessRuleData.isNotEmpty;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            // Check if all required fields are filled
            bool isFormValid = attributeNameController.text.trim().isNotEmpty &&
                leftOperandController.text.trim().isNotEmpty &&
                selectedOperator.isNotEmpty &&
                rightOperandController.text.trim().isNotEmpty &&
                successValueRangeController.text.trim().isNotEmpty &&
                selectedMultiConditionOperator.isNotEmpty &&
                warningValueRangeController.text.trim().isNotEmpty &&
                failureValueRangeController.text.trim().isNotEmpty &&
                successMessageController.text.trim().isNotEmpty &&
                warningMessageController.text.trim().isNotEmpty;

            // Add listeners to update validation state
            void updateValidation() {
              setState(() {});
            }

            // Add listeners for real-time validation
            attributeNameController.removeListener(updateValidation);
            attributeNameController.addListener(updateValidation);
            leftOperandController.removeListener(updateValidation);
            leftOperandController.addListener(updateValidation);
            rightOperandController.removeListener(updateValidation);
            rightOperandController.addListener(updateValidation);
            successValueRangeController.removeListener(updateValidation);
            successValueRangeController.addListener(updateValidation);
            warningValueRangeController.removeListener(updateValidation);
            warningValueRangeController.addListener(updateValidation);
            failureValueRangeController.removeListener(updateValidation);
            failureValueRangeController.addListener(updateValidation);
            successMessageController.removeListener(updateValidation);
            successMessageController.addListener(updateValidation);
            warningMessageController.removeListener(updateValidation);
            warningMessageController.addListener(updateValidation);

            return Dialog(
              backgroundColor: Colors.transparent,
              child: Container(
                width: 780,
                constraints: const BoxConstraints(maxHeight: 600),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: const BoxDecoration(
                        border: Border(
                          bottom:
                              BorderSide(color: Color(0xFFE5E7EB), width: 1),
                        ),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              title,
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.headlineSmall(context),
                                fontWeight: FontWeight.w600,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                          ),
                          Row(
                            children: [
                              if (!isEditMode)
                                ElevatedButton(
                                  onPressed: isFormValid
                                      ? () {
                                          // Save the current form data
                                          final businessRuleData = BusinessRule(
                                            attributeName:
                                                attributeNameController.text,
                                            operator: selectedOperator,
                                            leftOperand:
                                                leftOperandController.text,
                                            rightOperand:
                                                rightOperandController.text,
                                            successValueRange:
                                                successValueRangeController
                                                    .text,
                                            warningValueRange:
                                                warningValueRangeController
                                                    .text,
                                            failureValueRange:
                                                failureValueRangeController
                                                    .text,
                                            multiConditionOperator:
                                                selectedMultiConditionOperator,
                                            successMessage:
                                                successMessageController.text,
                                            warningMessage:
                                                warningMessageController.text,
                                          );

                                          // Update parent widget state
                                          this.setState(() {
                                            _businessRuleData
                                                .add(businessRuleData);
                                            _showBusinessRuleList = true;
                                          });

                                          // Update local dialog state
                                          setState(() {
                                            // Clear form for next entry
                                            attributeNameController.clear();
                                            leftOperandController.clear();
                                            rightOperandController.clear();
                                            successValueRangeController.clear();
                                            warningValueRangeController.clear();
                                            failureValueRangeController.clear();
                                            successMessageController.clear();
                                            warningMessageController.clear();
                                            selectedOperator = 'IS_UNIQUE';
                                            selectedMultiConditionOperator =
                                                'AND';
                                          });
                                        }
                                      : null,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: isFormValid
                                        ? const Color(0xFF0058FF)
                                        : Colors.grey[400],
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 16, vertical: 8),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    elevation: 0,
                                  ),
                                  child: Text(
                                    'Add more Rules',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodySmall(
                                          context),
                                      fontWeight: FontWeight.w500,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              const SizedBox(width: 8),
                              IconButton(
                                onPressed: () => Navigator.of(context).pop(),
                                icon: const Icon(Icons.close),
                                iconSize: 24,
                                color: Colors.grey[600],
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // Scrollable content area
                    Expanded(
                      child: Row(
                        children: [
                          // Left side - Form (70% always)
                          Expanded(
                            flex: 7,
                            child: SingleChildScrollView(
                              padding: const EdgeInsets.all(24),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Left column
                                  Expanded(
                                    child: Column(
                                      children: [
                                        _buildEditableFormField(
                                            context,
                                            'Attribute Name',
                                            attributeNameController),
                                        const SizedBox(height: 20),
                                        _buildEditableFormField(
                                            context,
                                            'Left Operand',
                                            leftOperandController),
                                        const SizedBox(height: 20),
                                        _buildEditableFormField(
                                            context,
                                            'Right Operand',
                                            rightOperandController),
                                        const SizedBox(height: 20),
                                        _buildEditableFormField(
                                            context,
                                            'Success Value/Range',
                                            successValueRangeController),
                                        const SizedBox(height: 20),
                                        _buildEditableFormField(
                                            context,
                                            'Failure Value/Range',
                                            failureValueRangeController),
                                        const SizedBox(height: 20),
                                        _buildEditableFormField(
                                            context,
                                            'Success Message',
                                            successMessageController),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 24),
                                  // Right column
                                  Expanded(
                                    child: Column(
                                      children: [
                                        _buildEditableDropdownField(
                                            context,
                                            'Operator',
                                            _getOperatorOptions(),
                                            selectedOperator, (value) {
                                          setState(() {
                                            selectedOperator = value!;
                                          });
                                        }),
                                        const SizedBox(height: 20),
                                        _buildEditableFormField(
                                            context,
                                            'Warning Value/Range',
                                            warningValueRangeController),
                                        const SizedBox(height: 20),
                                        _buildEditableDropdownField(
                                            context,
                                            'Multi Condition Operator',
                                            ['AND', 'OR', 'FALSE'],
                                            selectedMultiConditionOperator,
                                            (value) {
                                          setState(() {
                                            selectedMultiConditionOperator =
                                                value!;
                                          });
                                        }),
                                        const SizedBox(height: 20),
                                        _buildEditableFormField(
                                            context,
                                            'Warning Message',
                                            warningMessageController),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          // Right side - Always show (30% space)
                          Expanded(
                            flex: 3,
                            child: Container(
                              decoration: const BoxDecoration(
                                border: Border(
                                  left: BorderSide(
                                      color: Color(0xFFE5E7EB), width: 1),
                                ),
                                color: Colors.white,
                              ),
                              child: _showBusinessRuleList
                                  ? _buildBusinessRuleListPanel()
                                  : Container(
                                      width: double.infinity,
                                      height: double.infinity,
                                      color: Colors.white,
                                    ), // Empty white space when list is not shown
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Footer
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: const BoxDecoration(
                        border: Border(
                          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              'Cancel',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.grey[600],
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          ElevatedButton(
                            onPressed: () {
                              if (isEditMode && editIndex != null) {
                                // Update existing business rule
                                final updatedBusinessRule = BusinessRule(
                                  attributeName: attributeNameController.text,
                                  operator: selectedOperator,
                                  leftOperand: leftOperandController.text,
                                  rightOperand: rightOperandController.text,
                                  successValueRange:
                                      successValueRangeController.text,
                                  warningValueRange:
                                      warningValueRangeController.text,
                                  failureValueRange:
                                      failureValueRangeController.text,
                                  multiConditionOperator:
                                      selectedMultiConditionOperator,
                                  successMessage: successMessageController.text,
                                  warningMessage: warningMessageController.text,
                                );

                                // Update the parent widget state
                                this.setState(() {
                                  _businessRuleData[editIndex] =
                                      updatedBusinessRule;
                                });
                              }
                              Navigator.of(context).pop();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF0058FF),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              elevation: 0,
                            ),
                            child: Text(
                              isEditMode ? 'Update' : 'Validate',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// Get attribute names for business rule dropdown
  List<String> _getAttributeNames() {
    List<String> attributeNames = ['Select Attribute'];
    for (var attribute in _attributeData) {
      if (attribute.name != null && attribute.name!.isNotEmpty) {
        attributeNames.add(attribute.name!);
      }
    }
    // Add some default options if no attributes exist
    if (attributeNames.length == 1) {
      attributeNames.addAll(['email', 'username', 'age', 'phone', 'address']);
    }
    return attributeNames;
  }

  /// Get operator options for business rule dropdown
  List<String> _getOperatorOptions() {
    return [
      'IS_UNIQUE',
      'EXISTS_IN',
      'IS_VALID_DATE',
      'GREATER_THAN',
      'LESS_THAN',
      'EQUALS',
      'MATCHES_PATTERN',
      'IN_LIST',
      'GREATER_THAN_OR_EQUAL',
      'IS_NOT_NULL_WHEN',
      'EQUALS_TRUE_WHEN',
      'LESS_THAN_OR_EQUAL',
      'IS_NOT_NULL',
      'NOT_EQUALS',
      'BETWEEN',
      'NOT_BETWEEN',
      'IS_NULL',
      'NOT_IN_LIST',
      'NOT_EXISTS_IN',
      'STARTS_WITH',
      'ENDS_WITH',
      'CONTAINS',
      'NOT_CONTAINS',
      'IS_EMPTY',
      'IS_NOT_EMPTY',
      'LENGTH_EQUALS',
      'LENGTH_BETWEEN',
      'IS_POSITIVE',
      'IS_NEGATIVE',
      'IS_ZERO',
      'IS_INTEGER',
      'IS_DECIMAL',
      'DIVISIBLE_BY',
      'IS_FUTURE_DATE',
      'IS_PAST_DATE',
      'IS_TODAY',
      'DATE_DIFF_EQUALS',
      'DATE_DIFF_BETWEEN',
      'IS_WEEKDAY',
      'IS_WEEKEND',
      'IS_BUSINESS_DAY',
      'IS_VALID_EMAIL',
      'IS_VALID_URL',
      'IS_VALID_IP',
      'IS_VALID_CREDIT_CARD',
      'MATCHES_LUHN',
      'IS_ALPHANUMERIC',
      'IS_NUMERIC',
      'HAS_ALL_VALUES',
      'HAS_ANY_VALUE',
      'COUNT_EQUALS',
      'COUNT_GREATER_THAN',
      'COUNT_LESS_THAN',
      'IS_SUBSET_OF',
      'IS_WITHIN_RANGE',
      'EXCEEDS_LIMIT',
      'IS_AUTHORIZED_FOR',
      'HAS_PERMISSION',
      'IS_ACTIVE_STATUS',
      'IS_EXPIRED',
      'IF_THEN_ELSE',
      'WHEN_ALL_TRUE',
      'WHEN_ANY_TRUE',
      'UNLESS_CONDITION'
    ];
  }

  /// Build business rule list panel for the right side of the dialog
  Widget _buildBusinessRuleListPanel() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Business rule list
          _businessRuleData.isEmpty
              ? const Center(
                  child: Text(
                    'No business rules added yet',
                    style: TextStyle(color: Colors.grey),
                  ),
                )
              : Expanded(
                  child: ListView.builder(
                    padding: EdgeInsets.zero,
                    itemCount: _businessRuleData.length,
                    itemBuilder: (context, index) {
                      final rule = _businessRuleData[index];
                      return _buildBusinessRuleListItemCompact(
                          context, index, rule);
                    },
                  ),
                ),
        ],
      ),
    );
  }

  /// Build compact business rule list item for right panel
  Widget _buildBusinessRuleListItemCompact(
      BuildContext context, int index, BusinessRule rule) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Text(
            '${index + 1}. ',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          Expanded(
            child: Text(
              rule.attributeName ?? 'Business Rule ${index + 1}',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          InkWell(
            onTap: () => _editBusinessRuleInPlace(context, index),
            child:
                const Icon(Icons.edit_outlined, size: 16, color: Colors.blue),
          ),
          const SizedBox(width: 8),
          InkWell(
            onTap: () => _deleteBusinessRuleFromList(context, index),
            child:
                const Icon(Icons.delete_outline, size: 16, color: Colors.red),
          ),
        ],
      ),
    );
  }

  /// Edit business rule in place
  void _editBusinessRuleInPlace(BuildContext context, int index) {
    if (index < 0 || index >= _businessRuleData.length) return;

    final rule = _businessRuleData[index];

    // Prepare edit data
    final editData = {
      'attributeName': rule.attributeName ?? '',
      'leftOperand': rule.leftOperand ?? '',
      'rightOperand': rule.rightOperand ?? '',
      'operator': rule.operator ?? 'IS_UNIQUE',
      'successValueRange': rule.successValueRange ?? '',
      'warningValueRange': rule.warningValueRange ?? '',
      'failureValueRange': rule.failureValueRange ?? '',
      'multiConditionOperator': rule.multiConditionOperator ?? 'AND',
      'successMessage': rule.successMessage ?? '',
      'warningMessage': rule.warningMessage ?? '',
    };

    _showEnhancedBusinessRuleDialog(
      context: context,
      title: 'Edit Business Rule Configuration',
      isEditMode: true,
      editData: editData,
      editIndex: index,
    );
  }

  /// Delete business rule from list with confirmation
  void _deleteBusinessRuleFromList(BuildContext context, int index) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Delete Business Rule',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Text(
            'Are you sure you want to delete this business rule?',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[700],
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                // Update parent widget state
                this.setState(() {
                  _businessRuleData.removeAt(index);
                  // Hide the list if no rules remain
                  if (_businessRuleData.isEmpty) {
                    _showBusinessRuleList = false;
                  }
                });
                Navigator.of(context).pop();
              },
              child: Text(
                'Delete',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.red[600],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Enhanced attribute dialog with scrolling support
  void _showEnhancedAttributeDialog({
    required BuildContext context,
    required String title,
    required bool isEditMode,
    int? editIndex,
    Map<String, String>? editData,
  }) {
    // Controllers for form fields - using all table fields
    final attributeNameController =
        TextEditingController(text: editData?['name'] ?? '');
    final displayNameController =
        TextEditingController(text: editData?['displayName'] ?? '');
    final descriptionController =
        TextEditingController(text: editData?['description'] ?? '');
    final helperTextController =
        TextEditingController(text: editData?['helperText'] ?? '');
    final defaultValueController =
        TextEditingController(text: editData?['defaultValue'] ?? '');
    final enumValuesController =
        TextEditingController(text: editData?['enumValues'] ?? '');

    String selectedDataType = editData?['dataType'] ?? 'string';
    String selectedRequired = editData?['required'] ?? 'No';
    String selectedUnique = editData?['unique'] ?? 'No';
    String selectedKeyType = editData?['keyType'] ?? 'Select One';

    // Show attribute list if there are existing attributes, otherwise hide it
    _showAttributeList = _attributeData.isNotEmpty;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            // Check if all required fields are filled
            // Required fields: Attribute Name, Display Name, Data Type, Required, Unique, Default Value, Description, Helper text, Key type selection
            bool isFormValid = attributeNameController.text.trim().isNotEmpty &&
                displayNameController.text.trim().isNotEmpty &&
                selectedDataType.isNotEmpty &&
                selectedRequired.isNotEmpty &&
                selectedUnique.isNotEmpty &&
                defaultValueController.text.trim().isNotEmpty &&
                descriptionController.text.trim().isNotEmpty &&
                helperTextController.text.trim().isNotEmpty &&
                selectedKeyType != 'Select One' &&
                selectedKeyType.isNotEmpty;

            // Add listeners to update validation state
            void updateValidation() {
              setState(() {});
            }

            // Add listeners for real-time validation
            attributeNameController.removeListener(updateValidation);
            attributeNameController.addListener(updateValidation);
            displayNameController.removeListener(updateValidation);
            displayNameController.addListener(updateValidation);
            descriptionController.removeListener(updateValidation);
            descriptionController.addListener(updateValidation);
            helperTextController.removeListener(updateValidation);
            helperTextController.addListener(updateValidation);
            defaultValueController.removeListener(updateValidation);
            defaultValueController.addListener(updateValidation);

            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Container(
                width: 780,
                height: math.min(MediaQuery.of(context).size.height * 0.8, 600),
                child: Column(
                  children: [
                    // Header with bottom border
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: const BoxDecoration(
                        border: Border(
                          bottom:
                              BorderSide(color: Color(0xFFE5E7EB), width: 1),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            title,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.titleLarge(context),
                              fontWeight: FontWeight.w600,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                          Row(
                            children: [
                              if (!isEditMode)
                                ElevatedButton(
                                  onPressed: isFormValid
                                      ? () {
                                          // Save the current form data
                                          final attributeData = ObjectAttribute(
                                            name: attributeNameController.text,
                                            displayName:
                                                displayNameController.text,
                                            dataType: selectedDataType,
                                            required: selectedRequired == 'Yes',
                                            unique: selectedUnique == 'Yes',
                                            defaultValue: defaultValueController
                                                .text
                                                .trim(),
                                            description:
                                                descriptionController.text,
                                            helperText:
                                                helperTextController.text,
                                            isPrimaryKey: selectedKeyType ==
                                                'Primary key',
                                            isForeignKey: selectedKeyType ==
                                                'Foreign key',
                                          );

                                          setState(() {
                                            _attributeData.add(attributeData);
                                            _showAttributeList = true;

                                            // Clear form for next entry
                                            attributeNameController.clear();
                                            displayNameController.clear();
                                            defaultValueController.clear();
                                            descriptionController.clear();
                                            helperTextController.clear();
                                            selectedDataType = 'string';
                                            selectedRequired = 'No';
                                            selectedUnique = 'No';
                                            selectedKeyType = 'Select One';
                                          });

                                          // print('Attribute added: ${attributeData.name}');
                                        }
                                      : null,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: isFormValid
                                        ? const Color(0xFF0058FF)
                                        : Colors.grey[400],
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 16, vertical: 8),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    elevation: 0,
                                  ),
                                  child: Text(
                                    'Add More',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodySmall(
                                          context),
                                      fontWeight: FontWeight.w500,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              const SizedBox(width: 8),
                              IconButton(
                                onPressed: () => Navigator.of(context).pop(),
                                icon: const Icon(Icons.close),
                                iconSize: 24,
                                color: Colors.grey[600],
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // Scrollable content area
                    Expanded(
                      child: Row(
                        children: [
                          // Left side - Form (70% always)
                          Expanded(
                            flex: 7,
                            child: SingleChildScrollView(
                              padding: const EdgeInsets.all(24),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Left column
                                  Expanded(
                                    child: Column(
                                      children: [
                                        _buildEditableFormField(
                                            context,
                                            'Attribute Name',
                                            attributeNameController),
                                        const SizedBox(height: 20),
                                        _buildEditableDropdownField(
                                            context,
                                            'Data Type',
                                            [
                                              'string',
                                              'integer',
                                              'decimal',
                                              'boolean',
                                              'date',
                                              'datetime',
                                              'text',
                                              'Enum',
                                              'Object',
                                              'Array',
                                              'MultiValue'
                                            ],
                                            selectedDataType, (value) {
                                          setState(() {
                                            selectedDataType = value!;
                                          });
                                        }),
                                        const SizedBox(height: 20),
                                        _buildEditableDropdownField(
                                            context,
                                            'Unique',
                                            ['No', 'Yes'],
                                            selectedUnique, (value) {
                                          setState(() {
                                            selectedUnique = value!;
                                          });
                                        }),
                                        const SizedBox(height: 20),
                                        _buildEditableFormField(
                                            context,
                                            'Description',
                                            descriptionController),
                                        const SizedBox(height: 20),
                                        // Select One label with buttons
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Select One',
                                              style: FontManager.getCustomStyle(
                                                fontSize: ResponsiveFontSizes
                                                    .bodyMedium(context),
                                                fontWeight: FontWeight.w500,
                                                fontFamily: FontManager
                                                    .fontFamilyTiemposText,
                                                color: Colors.black87,
                                              ),
                                            ),
                                            const SizedBox(height: 8),
                                            Row(
                                              children: [
                                                _buildKeyTypeButton(
                                                    'Primary key',
                                                    selectedKeyType ==
                                                        'Primary key', () {
                                                  setState(() {
                                                    // Toggle behavior: if already selected, deselect it
                                                    if (selectedKeyType ==
                                                        'Primary key') {
                                                      selectedKeyType =
                                                          'Select One';
                                                    } else {
                                                      selectedKeyType =
                                                          'Primary key';
                                                    }
                                                  });
                                                }),
                                                const SizedBox(width: 8),
                                                _buildKeyTypeButton(
                                                    'Foreign key',
                                                    selectedKeyType ==
                                                        'Foreign key', () {
                                                  setState(() {
                                                    // Toggle behavior: if already selected, deselect it
                                                    if (selectedKeyType ==
                                                        'Foreign key') {
                                                      selectedKeyType =
                                                          'Select One';
                                                    } else {
                                                      selectedKeyType =
                                                          'Foreign key';
                                                    }
                                                  });
                                                }),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 24),
                                  // Right column
                                  Expanded(
                                    child: Column(
                                      children: [
                                        _buildEditableFormField(
                                            context,
                                            'Display Name',
                                            displayNameController),
                                        const SizedBox(height: 20),
                                        _buildEditableDropdownField(
                                            context,
                                            'Required',
                                            ['No', 'Yes'],
                                            selectedRequired, (value) {
                                          setState(() {
                                            selectedRequired = value!;
                                          });
                                        }),
                                        const SizedBox(height: 20),
                                        _buildEditableFormField(
                                            context,
                                            'Default Value',
                                            defaultValueController),
                                        const SizedBox(height: 20),
                                        _buildEditableFormField(
                                            context,
                                            'Helper text',
                                            helperTextController),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          // Right side - Always show (30% space)
                          Expanded(
                            flex: 3,
                            child: Container(
                              decoration: const BoxDecoration(
                                border: Border(
                                  left: BorderSide(
                                      color: Color(0xFFE5E7EB), width: 1),
                                ),
                                color: Colors.white,
                              ),
                              child: _showAttributeList
                                  ? _buildAttributeListPanel()
                                  : Container(
                                      width: double.infinity,
                                      height: double.infinity,
                                      color: Colors.white,
                                    ), // Empty white space when list is not shown
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Footer with top border
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: const BoxDecoration(
                        border: Border(
                          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                                side: BorderSide(color: Colors.grey[300]!),
                              ),
                            ),
                            child: Text(
                              'Cancel',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.grey[700],
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          ElevatedButton(
                            onPressed: () {
                              if (isEditMode && editIndex != null) {
                                // Update existing attribute
                                final updatedAttribute = ObjectAttribute(
                                  name: attributeNameController.text,
                                  displayName: displayNameController.text,
                                  dataType: selectedDataType,
                                  required: selectedRequired == 'Yes',
                                  unique: selectedUnique == 'Yes',
                                  defaultValue:
                                      defaultValueController.text.trim(),
                                  description: descriptionController.text,
                                  helperText: helperTextController.text,
                                  isPrimaryKey:
                                      selectedKeyType == 'Primary key',
                                  isForeignKey:
                                      selectedKeyType == 'Foreign key',
                                );

                                setState(() {
                                  _attributeData[editIndex] = updatedAttribute;
                                });
                              }
                              Navigator.of(context).pop();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF0058FF),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              elevation: 0,
                            ),
                            child: Text(
                              isEditMode ? 'Update' : 'Validate',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// Handle attribute form submission
  void _handleAttributeSubmit(
    BuildContext context,
    TextEditingController attributeNameController,
    TextEditingController displayNameController,
    TextEditingController descriptionController,
    TextEditingController helperTextController,
    TextEditingController defaultValueController,
    TextEditingController enumValuesController,
    String selectedDataType,
    String selectedRequired,
    String selectedUnique,
    String selectedDefaultType,
    String selectedValidation,
    bool isEditMode,
    int? editIndex,
  ) {
    // Validate required fields
    if (attributeNameController.text.isEmpty ||
        displayNameController.text.isEmpty) {
      return;
    }

    final attributeData = ObjectAttribute(
      name: attributeNameController.text,
      displayName: displayNameController.text,
      dataType: selectedDataType,
      required: selectedRequired == 'Yes',
      unique: selectedUnique == 'Yes',
      defaultType: selectedDefaultType,
      defaultValue: defaultValueController.text.isEmpty
          ? null
          : defaultValueController.text,
      description: descriptionController.text.isEmpty
          ? null
          : descriptionController.text,
      helperText:
          helperTextController.text.isEmpty ? null : helperTextController.text,
      enumValues: enumValuesController.text.isEmpty
          ? null
          : enumValuesController.text.split(',').map((e) => e.trim()).toList(),
    );

    setState(() {
      if (isEditMode && editIndex != null) {
        _attributeData[editIndex] = attributeData;
      } else {
        _attributeData.add(attributeData);
      }
    });

    // If not in edit mode, just save the data and keep the form
    if (!isEditMode) {
      // Don't clear the form, just save the data
      // The form will remain filled for user to see what was saved
      // User can manually clear or modify for next entry
    } else {
      // For edit mode, close the dialog
      Navigator.of(context).pop();
    }
  }

  /// Show attribute list dialog with edit/delete functionality
  void _showAttributeListDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            width: 600,
            height: math.min(MediaQuery.of(context).size.height * 0.8, 500),
            child: Column(
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Attribute Configuration',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.titleLarge(context),
                          fontWeight: FontWeight.w600,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                        iconSize: 24,
                        color: Colors.grey[600],
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                ),

                // Attribute list
                Expanded(
                  child: _attributeData.isEmpty
                      ? Center(
                          child: Text(
                            'No attributes added yet',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.grey[600],
                            ),
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: _attributeData.length,
                          itemBuilder: (context, index) {
                            final attribute = _attributeData[index];
                            return _buildAttributeListItem(
                                context, index, attribute);
                          },
                        ),
                ),

                // Footer with Add More button
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: const BoxDecoration(
                    border: Border(
                      top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 24, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                            side: BorderSide(color: Colors.grey[300]!),
                          ),
                        ),
                        child: Text(
                          'Cancel',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.grey[700],
                          ),
                        ),
                      ),
                      ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          _showAddAttributeModal(context);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF0058FF),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 24, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          elevation: 0,
                        ),
                        child: Text(
                          'Add More',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Build individual attribute list item with edit/delete functionality
  Widget _buildAttributeListItem(
      BuildContext context, int index, ObjectAttribute attribute) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  attribute.name ?? '',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodyLarge(context),
                    fontWeight: FontWeight.w600,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Type: ${attribute.dataType} | Required: ${(attribute.required ?? false) ? 'Yes' : 'No'}',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Row(
            children: [
              IconButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _showEditAttributeModal(
                    context,
                    index,
                    attribute.name ?? '',
                    attribute.displayName ?? '',
                    attribute.dataType ?? '',
                    (attribute.required ?? false) ? 'Yes' : 'No',
                    (attribute.unique ?? false) ? 'Yes' : 'No',
                  );
                },
                icon: const Icon(Icons.edit_outlined),
                color: Colors.blue[600],
                iconSize: 20,
                tooltip: 'Edit',
              ),
              IconButton(
                onPressed: () => _deleteAttributeFromList(context, index),
                icon: const Icon(Icons.delete_outline),
                color: Colors.red[600],
                iconSize: 20,
                tooltip: 'Delete',
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Delete attribute from list with confirmation
  void _deleteAttributeFromList(BuildContext context, int index) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Delete Attribute',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Text(
            'Are you sure you want to delete this attribute?',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[700],
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _attributeData.removeAt(index);
                });
                Navigator.of(context).pop();
                Navigator.of(context).pop();
                _showAttributeListDialog(context);
              },
              child: Text(
                'Delete',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.red[600],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Edit relationship in place
  void _editRelationshipInPlace(BuildContext context, int index) {
    final relationship = _relationshipData[index];
    // Convert EntityRelationship object to map for the dialog
    final relationshipData = {
      'primaryKey': relationship.primaryKey,
      'foreignKey': relationship.foreignKey,
      'relatedEntity': relationship.relatedEntity,
      'relationshipType': relationship.relationshipType,
      'onDelete': relationship.onDelete,
      'onUpdate': relationship.onUpdate,
      'foreignKeyType': relationship.foreignKeyType,
      'description': relationship.description,
    };
    _showEnhancedRelationshipDialog(
      context: context,
      title: 'Edit Entity Relationship Configuration',
      isEditMode: true,
      editData: relationshipData,
      editIndex: index,
    );
  }

  /// Delete relationship from list with confirmation
  void _deleteRelationshipFromList(BuildContext context, int index) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Delete Relationship',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Text(
            'Are you sure you want to delete this relationship?',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[700],
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _relationshipData.removeAt(index);
                  // Hide the right panel if no items remain
                  if (_relationshipData.isEmpty) {
                    _showRelationshipList = false;
                  }
                });
                Navigator.of(context).pop();
              },
              child: Text(
                'Delete',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.red[600],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Edit security classification in place
  void _editSecurityClassificationInPlace(BuildContext context, int index) {
    if (index < _securityClassificationData.length) {
      final classification = _securityClassificationData[index];
      _showEnhancedSecurityClassificationDialog(
        context: context,
        title: 'Edit Security Classification',
        isEditMode: true,
        editData: classification,
        editIndex: index,
      );
    }
  }

  /// Delete security classification from list with confirmation
  void _deleteSecurityClassificationFromList(BuildContext context, int index) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Delete Security Classification',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Text(
            'Are you sure you want to delete this security classification?',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[700],
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  if (index < _securityClassificationData.length) {
                    _securityClassificationData.removeAt(index);
                    // Hide the right panel if no items remain
                    if (_securityClassificationData.isEmpty) {
                      _showSecurityClassificationList = false;
                    }
                  }
                });
                Navigator.of(context).pop();
              },
              child: Text(
                'Delete',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.red[600],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Edit system permissions in place
  void _editSystemPermissionsInPlace(BuildContext context, int index) {
    if (index < _systemPermissionData.length) {
      final permission = _systemPermissionData[index];
      _showEnhancedSystemPermissionsDialog(
        context: context,
        title: 'Edit System Permission',
        isEditMode: true,
        editData: permission,
        editIndex: index,
      );
    }
  }

  /// Delete system permissions from list with confirmation
  void _deleteSystemPermissionsFromList(BuildContext context, int index) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Delete System Permission',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Text(
            'Are you sure you want to delete this system permission?',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[700],
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  if (index < _systemPermissionData.length) {
                    _systemPermissionData.removeAt(index);
                    // Hide the right panel if no items remain
                    if (_systemPermissionData.isEmpty) {
                      _showSystemPermissionsList = false;
                    }
                  }
                });
                Navigator.of(context).pop();
              },
              child: Text(
                'Delete',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.red[600],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Edit enumerated values in place
  void _editEnumeratedValuesInPlace(BuildContext context, int index) {
    if (index < _enumValueData.length) {
      final enumValue = _enumValueData[index];
      _showEnhancedEnumeratedValuesDialog(
        context: context,
        title: 'Edit Enumerated Value',
        isEditMode: true,
        editData: enumValue,
        editIndex: index,
      );
    }
  }

  /// Build action button for multi-select style
  Widget _buildActionButton(String label, bool isSelected, Function(bool) onChanged) {
    return InkWell(
      onTap: () => onChanged(!isSelected),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF0058FF) : Colors.white,
          border: Border.all(
            color: isSelected ? const Color(0xFF0058FF) : const Color(0xFFE5E7EB),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: isSelected ? Colors.white : Colors.black,
          ),
        ),
      ),
    );
  }

  /// Delete enumerated values from list with confirmation
  void _deleteEnumeratedValuesFromList(BuildContext context, int index) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Delete Enumerated Value',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Text(
            'Are you sure you want to delete this enumerated value?',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[700],
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  if (index < _enumValueData.length) {
                    _enumValueData.removeAt(index);
                    // Hide the right panel if no items remain
                    if (_enumValueData.isEmpty) {
                      _showEnumValuesList = false;
                    }
                  }
                });
                Navigator.of(context).pop();
              },
              child: Text(
                'Delete',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.red[600],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Build key type button (Select One, Primary key, Foreign key)
  Widget _buildKeyTypeButton(
      String label, bool isSelected, VoidCallback? onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? const Color(0xFF0058FF) : Colors.grey[300]!,
          ),
          borderRadius: BorderRadius.circular(4),
          color: isSelected
              ? const Color(0xFF0058FF).withValues(alpha: 0.1)
              : Colors.white,
        ),
        child: Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodySmall(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: isSelected ? const Color(0xFF0058FF) : Colors.grey[600],
          ),
        ),
      ),
    );
  }

  /// Build attribute list panel for the right side
  Widget _buildAttributeListPanel() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Attribute list
          _attributeData.isEmpty
              ? const Center(
                  child: Text(
                    'No attributes added yet',
                    style: TextStyle(color: Colors.grey),
                  ),
                )
              : Expanded(
                  child: ListView.builder(
                    padding: EdgeInsets.zero,
                    itemCount: _attributeData.length,
                    itemBuilder: (context, index) {
                      final attribute = _attributeData[index];
                      return _buildAttributeListItemCompact(
                          context, index, attribute);
                    },
                  ),
                ),
        ],
      ),
    );
  }

  /// Build relationship list panel for the right side
  Widget _buildRelationshipListPanel() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Relationship list
          _relationshipData.isEmpty
              ? const Center(
                  child: Text(
                    'No relationships added yet',
                    style: TextStyle(color: Colors.grey),
                  ),
                )
              : Expanded(
                  child: ListView.builder(
                    padding: EdgeInsets.zero,
                    itemCount: _relationshipData.length,
                    itemBuilder: (context, index) {
                      final relationship = _relationshipData[index];
                      return _buildRelationshipListItemCompact(
                          context, index, relationship);
                    },
                  ),
                ),
        ],
      ),
    );
  }

  /// Build security classification list panel for the right side
  Widget _buildSecurityClassificationListPanel() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Security classification list
          _securityClassificationData.isEmpty
              ? const Center(
                  child: Text(
                    'No security classifications added yet',
                    style: TextStyle(color: Colors.grey),
                  ),
                )
              : Expanded(
                  child: ListView.builder(
                    padding: EdgeInsets.zero,
                    itemCount: _securityClassificationData.length,
                    itemBuilder: (context, index) {
                      final classification = _securityClassificationData[index];
                      return _buildSecurityClassificationListItemCompact(context, index, classification);
                    },
                  ),
                ),
        ],
      ),
    );
  }

  /// Build system permissions list panel for the right side
  Widget _buildSystemPermissionsListPanel() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // System permissions list
          _systemPermissionData.isEmpty
              ? const Center(
                  child: Text(
                    'No system permissions added yet',
                    style: TextStyle(color: Colors.grey),
                  ),
                )
              : Expanded(
                  child: ListView.builder(
                    padding: EdgeInsets.zero,
                    itemCount: _systemPermissionData.length,
                    itemBuilder: (context, index) {
                      final permission = _systemPermissionData[index];
                      return _buildSystemPermissionsListItemCompact(context, index, permission);
                    },
                  ),
                ),
        ],
      ),
    );
  }

  /// Build enumerated values list panel for the right side
  Widget _buildEnumeratedValuesListPanel() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Enumerated values list
          _enumValueData.isEmpty
              ? const Center(
                  child: Text(
                    'No enumerated values added yet',
                    style: TextStyle(color: Colors.grey),
                  ),
                )
              : Expanded(
                  child: ListView.builder(
                    padding: EdgeInsets.zero,
                    itemCount: _enumValueData.length,
                    itemBuilder: (context, index) {
                      final enumValue = _enumValueData[index];
                      return _buildEnumeratedValuesListItemCompact(context, index, enumValue);
                    },
                  ),
                ),
        ],
      ),
    );
  }

  /// Build compact attribute list item for right panel
  Widget _buildAttributeListItemCompact(
      BuildContext context, int index, ObjectAttribute attribute) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Text(
            '${index + 1}. ',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          Expanded(
            child: Text(
              attribute.name ?? 'Attribute Name',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          InkWell(
            onTap: () => _editAttributeInPlace(context, index),
            child:
                const Icon(Icons.edit_outlined, size: 16, color: Colors.blue),
          ),
          const SizedBox(width: 8),
          InkWell(
            onTap: () => _deleteAttributeFromList(context, index),
            child:
                const Icon(Icons.delete_outline, size: 16, color: Colors.red),
          ),
        ],
      ),
    );
  }

  /// Build compact relationship list item for right panel
  Widget _buildRelationshipListItemCompact(
      BuildContext context, int index, EntityRelationship relationship) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Text(
            '${index + 1}. ',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          Expanded(
            child: Text(
              '${relationship.primaryKey ?? 'Primary Key'} -> ${relationship.relatedEntity ?? 'Related Entity'}',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          InkWell(
            onTap: () => _editRelationshipInPlace(context, index),
            child:
                const Icon(Icons.edit_outlined, size: 16, color: Colors.blue),
          ),
          const SizedBox(width: 8),
          InkWell(
            onTap: () => _deleteRelationshipFromList(context, index),
            child:
                const Icon(Icons.delete_outline, size: 16, color: Colors.red),
          ),
        ],
      ),
    );
  }

  /// Build compact security classification list item for right panel
  Widget _buildSecurityClassificationListItemCompact(BuildContext context, int index, SecurityClassification classification) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Text(
            '${index + 1}. ',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          Expanded(
            child: Text(
              '${classification.entityAttribute ?? 'Entity'} - ${classification.classification ?? 'Classification'}',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          InkWell(
            onTap: () => _editSecurityClassificationInPlace(context, index),
            child: const Icon(Icons.edit_outlined, size: 16, color: Colors.blue),
          ),
          const SizedBox(width: 8),
          InkWell(
            onTap: () => _deleteSecurityClassificationFromList(context, index),
            child: const Icon(Icons.delete_outline, size: 16, color: Colors.red),
          ),
        ],
      ),
    );
  }

  /// Build compact system permissions list item for right panel
  Widget _buildSystemPermissionsListItemCompact(BuildContext context, int index, SystemPermission permission) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Text(
            '${index + 1}. ',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          Expanded(
            child: Text(
              '${permission.permissionName ?? 'Permission'} - ${permission.permissionType ?? 'Type'}',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          InkWell(
            onTap: () => _editSystemPermissionsInPlace(context, index),
            child: const Icon(Icons.edit_outlined, size: 16, color: Colors.blue),
          ),
          const SizedBox(width: 8),
          InkWell(
            onTap: () => _deleteSystemPermissionsFromList(context, index),
            child: const Icon(Icons.delete_outline, size: 16, color: Colors.red),
          ),
        ],
      ),
    );
  }

  /// Build compact enumerated values list item for right panel
  Widget _buildEnumeratedValuesListItemCompact(BuildContext context, int index, EnumValue enumValue) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Text(
            '${index + 1}. ',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          Expanded(
            child: Text(
              '${enumValue.enumName ?? 'Enum'} - ${enumValue.value ?? 'Value'}',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          InkWell(
            onTap: () => _editEnumeratedValuesInPlace(context, index),
            child: const Icon(Icons.edit_outlined, size: 16, color: Colors.blue),
          ),
          const SizedBox(width: 8),
          InkWell(
            onTap: () => _deleteEnumeratedValuesFromList(context, index),
            child: const Icon(Icons.delete_outline, size: 16, color: Colors.red),
          ),
        ],
      ),
    );
  }

  /// Edit attribute in place (populate form with existing data)
  void _editAttributeInPlace(BuildContext context, int index) {
    // This will be handled by populating the form fields with existing data
    // For now, we'll use the existing edit modal
    final attribute = _attributeData[index];
    _showEditAttributeModal(
      context,
      index,
      attribute.name ?? '',
      attribute.displayName ?? '',
      attribute.dataType ?? '',
      (attribute.required ?? false) ? 'Yes' : 'No',
      (attribute.unique ?? false) ? 'Yes' : 'No',
    );
  }

  // void _showEditAttributeModal(
  //     BuildContext context,
  //     int index,
  //     String attributeName,
  //     String displayName,
  //     String dataType,
  //     String required,
  //     String unique) {
  //   // Get the current attribute data to populate all fields
  //   final currentData = _attributeData[index];

  //   _showEnhancedAttributeDialog(
  //     context: context,
  //     title: 'Edit Attribute',
  //     isEditMode: true,
  //     editIndex: index,
  //     editData: currentData,
  //   );
  // }
  void _showEditAttributeModal(
      BuildContext context,
      int index,
      String attributeName,
      String displayName,
      String dataType,
      String required,
      String unique) {
    // Use static data from _attributeData
    Map<String, String> currentData = {};
    if (index < _attributeData.length) {
      final attribute = _attributeData[index];
      currentData = {
        'name': attribute.name ?? attributeName,
        'displayName': attribute.displayName ?? displayName,
        'dataType': attribute.dataType ?? dataType,
        'required': (attribute.required ?? false) ? 'Yes' : 'No',
        'unique': (attribute.unique ?? false) ? 'Yes' : 'No',
        'defaultType': attribute.defaultType ?? '',
        'defaultValue': attribute.defaultValue ?? '',
        'description': attribute.description ?? '',
        'helperText': attribute.helperText ?? '',
        'enumValues': attribute.enumValues?.join(', ') ?? '',
        'validation': (attribute.required ?? false) ? 'Required' : 'Optional',
        'keyType': attribute.isPrimaryKey
            ? 'Primary key'
            : attribute.isForeignKey
                ? 'Foreign key'
                : 'Select One',
      };
    } else {
      // Fallback to parameters if index is out of range
      currentData = {
        'name': attributeName,
        'displayName': displayName,
        'dataType': dataType,
        'required': required,
        'unique': unique,
        'defaultType': '',
        'defaultValue': '',
        'description': '',
        'helperText': '',
        'enumValues': '',
        'validation': required == 'Yes' ? 'Required' : 'Optional',
        'keyType': 'Select One',
      };
    }

    _showEnhancedAttributeDialog(
      context: context,
      title: 'Edit Attribute Configuration',
      isEditMode: true,
      editIndex: index,
      editData: currentData,
    );
  }

  void _deleteAttribute(BuildContext context, int index, String attributeName) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: Text(
            'Delete Attribute',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Text(
            'Are you sure you want to delete the attribute "$attributeName"? This action cannot be undone.',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[700],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[700],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                // Remove the attribute from the list
                setState(() {
                  _attributeData.removeAt(index);
                });
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[600],
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Delete',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteConfirmationAttribute(
      BuildContext context, int index, String attributeName) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: Text(
            'Delete Attribute',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Text(
            'Are you sure you want to delete the attribute "$attributeName"? This action cannot be undone.',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[700],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[700],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                // Remove the attribute from the list
                setState(() {
                  _attributeData.removeAt(index);
                });
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[600],
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Delete',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteConfirmation(
      BuildContext context, int index, String attributeName) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: Text(
            'Delete Attribute',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Text(
            'Are you sure you want to delete the attribute "$attributeName"? This action cannot be undone.',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[700],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[700],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                // Handle delete logic here
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[600],
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Delete',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildModalFormField(BuildContext context, String label, String hint) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[500],
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildModalDropdownField(
      BuildContext context, String label, List<String> options) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          icon: Container(
            alignment: Alignment.centerRight,
            child: const Icon(
              Icons.keyboard_arrow_down,
              size: 24,
            ),
          ),
          iconSize: 24,
          value: options.first,
          items: options.map((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(
                value,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            );
          }).toList(),
          onChanged: (String? newValue) {
            // Handle dropdown change
          },
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildEditableFormField(
      BuildContext context, String label, TextEditingController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildEditableDropdownField(BuildContext context, String label,
      List<String> options, String selectedValue, Function(String?) onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          child: DropdownButtonFormField<String>(
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Color(0xFF0058FF)),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              filled: true,
              fillColor: Colors.white,
            ),
            icon: Container(
              alignment: Alignment.centerRight,
              child: const Icon(
                Icons.keyboard_arrow_down,
                size: 24,
              ),
            ),
            iconSize: 24,
            isExpanded: true,
            value:
                options.contains(selectedValue) ? selectedValue : options.first,
            items: options.map((String value) {
              return DropdownMenuItem<String>(
                value: value,
                child: Container(
                  width: double.infinity,
                  child: Text(
                    value,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontWeight.w400,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              );
            }).toList(),
            onChanged: onChanged,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildModalFormFieldWithValue(
      BuildContext context, String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          initialValue: value,
          decoration: InputDecoration(
            hintStyle: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[500],
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildModalDropdownFieldWithValue(BuildContext context, String label,
      List<String> options, String selectedValue) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          icon: Container(
            alignment: Alignment.centerRight,
            child: const Icon(
              Icons.keyboard_arrow_down,
              size: 24,
            ),
          ),
          iconSize: 24,
          value:
              options.contains(selectedValue) ? selectedValue : options.first,
          items: options.map((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(
                value,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            );
          }).toList(),
          onChanged: (String? newValue) {
            // Handle dropdown change
          },
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildFormField(BuildContext context, String label, String hint) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[500],
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField(
      BuildContext context, String label, List<String> options) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          ),
          items: options.map((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(
                value,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            );
          }).toList(),
          onChanged: (String? newValue) {
            // Handle dropdown change
          },
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildPlaceholderContent(BuildContext context, String title,
      {ObjectCreationModel? objectData}) {
    return Container(
      padding: const EdgeInsets.all(6),
      margin: EdgeInsets.symmetric(horizontal: 6),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and Add button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _getPlaceholderTitle(title),
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              IgnorePointer(
                ignoring: !Provider.of<WebHomeProviderStatic>(context).isAIMode,
                child: Opacity(
                  opacity: !Provider.of<WebHomeProviderStatic>(context).isAIMode
                      ? 0.5
                      : 1.0,
                  child: ElevatedButton.icon(
                    onPressed: () => title == 'Entity Relationships'
                        ? _showAddEntityRelationshipModal(context)
                        : title == 'Attribute Business Rules'
                            ? _showAddBusinessRuleModal(context)
                            : _showAddModal(context, title),
                    icon: Icon(
                        title == 'Entity Relationships'
                            ? Icons.link
                            : Icons.add,
                        size: 16,
                        color: Colors.white),
                    label: Text(
                      _getAddButtonText(title),
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyTiemposText,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0058FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 6),
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                      alignment: Alignment.center,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Table container with internal scroll and fixed last column
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFE5E7EB)),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Row(
              children: [
                // Scrollable table section (header + body)
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Column(
                      children: [
                        // Scrollable header
                        Container(
                          decoration: const BoxDecoration(
                            color: Color(0xFFF9FAFB),
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(6),
                            ),
                          ),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 8),
                          child:
                              _buildScrollableFormTableHeader(context, title),
                        ),
                        // Scrollable body
                        Container(
                          constraints: const BoxConstraints(maxHeight: 200),
                          child: SingleChildScrollView(
                            child: Column(
                              children: _buildScrollableFormTableRows(
                                  context, title,
                                  objectData: objectData),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Fixed Actions column (header + body)
                Container(
                  // width: 120,
                  decoration: const BoxDecoration(
                    border: Border(
                      left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      // Fixed Actions header
                      Container(
                        decoration: const BoxDecoration(
                          color: Color(0xFFF9FAFB),
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(6),
                          ),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 14, vertical: 8),
                        child: Text(
                          'ACTIONS',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.labelMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.grey[700],
                          ),
                        ),
                      ),
                      // Fixed Actions body
                      Container(
                        constraints: const BoxConstraints(maxHeight: 200),
                        child: SingleChildScrollView(
                          child: Column(
                            children: _buildFixedActionsColumn(context, title,
                                objectData: objectData),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableHeader(BuildContext context, String title) {
    List<String> headers = _getTableHeaders(title);
    List<double> widths = _getColumnWidths(title);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Row(
        children: headers.asMap().entries.map((entry) {
          int index = entry.key;
          String header = entry.value;
          double width = widths[index];

          return SizedBox(
            width: width,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                header,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[700],
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  List<Widget> _buildTableRows(BuildContext context, String title) {
    List<List<String>> rowsData = _getTableData(title);
    List<double> widths = _getColumnWidths(title);

    return rowsData.map((rowData) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        decoration: const BoxDecoration(
          border: Border(
            top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
          ),
        ),
        child: Row(
          children: rowData.asMap().entries.map((entry) {
            int index = entry.key;
            String data = entry.value;
            double width = widths[index];

            return SizedBox(
              width: width,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Tooltip(
                  message: data,
                  child: Text(
                    data,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodySmall(context),
                      fontWeight: FontWeight.w400,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      );
    }).toList();
  }

  List<int> _getColumnFlexValues(String title) {
    switch (title) {
      case 'Object Details':
      // return [
      //   2,
      //   2,
      //   2,
      //   2,
      //   4
      // ]; // PROPERTY NAME, VALUE, TYPE, REQUIRED, DESCRIPTION
      case 'Entity Relationships':
      // return [
      //   3,
      //   3,
      //   2,
      //   2,
      //   2
      // ]; // RELATIONSHIP NAME, TARGET ENTITY, TYPE, CARDINALITY, STATUS
      case 'Attribute Business Rules':
      // return [
      //   3,
      //   2,
      //   3,
      //   2,
      //   2
      // ]; // RULE NAME, ATTRIBUTE, CONDITION, ACTION, PRIORITY
      case 'Enumerated Values':
      // return [
      //   3,
      //   4,
      //   2,
      //   3,
      //   2
      // ]; // ENUM NAME, VALUES, DEFAULT, DESCRIPTION, STATUS
      case 'System Permissions':
      // return [
      //   3,
      //   2,
      //   2,
      //   3,
      //   2
      // ]; // PERMISSION NAME, ROLE, ACCESS LEVEL, RESOURCE, STATUS
      case 'Security Classification':
      // return [
      //   3,
      //   2,
      //   3,
      //   3,
      //   2
      // ]; // CLASSIFICATION, LEVEL, ATTRIBUTES, RESTRICTIONS, STATUS
      default:
        return [3, 3, 2, 2]; // NAME, VALUE, TYPE, STATUS
    }
  }

  List<String> _getTableHeaders(String title) {
    switch (title) {
      case 'Object Details':
        return ['FIELD', 'VALUE', 'SOURCE'];
      case 'Entity Relationships':
        return [
          'RELATEDENTITY',
          'RELATIONSHIPTYPE',
          'FOREIGNKEY',
          'DESCRIPTION',
          'STATUS'
        ];
      case 'Attribute Business Rules':
        return ['ATTRIBUTENAME', 'OPERATOR', 'VALUE', 'ERRORMESSAGE', 'STATUS'];
      case 'Enumerated Values':
        return [
          'ENTITYATTRIBUTE',
          'ENUMNAME',
          'VALUE',
          'DISPLAY',
          'DESCRIPTION',
          'SORTORDER',
          'ACTIVE'
        ];
      case 'System Permissions':
        return [
          'PERMISSIONID',
          'PERMISSIONNAME',
          'PERMISSIONTYPE',
          'RESOURCEIDENTIFIER',
          'ACTIONS',
          'DESCRIPTION',
          'SCOPE',
          'NATURALLANGUAGE',
          'VERSION',
          'STATUS'
        ];
      case 'Security Classification':
        return [
          'ENTITYATTRIBUTE',
          'CLASSIFICATION',
          'PIITYPE',
          'ENCRYPTIONREQUIRED',
          'ENCRYPTIONTYPE',
          'MASKINGREQUIRED',
          'MASKINGPATTERN',
          'ACCESSLEVEL',
          'AUDITTRAIL'
        ];
      case 'Role System Permissions':
        return [
          'ROLEID',
          'PERMISSIONID',
          'GRANTEDACTIONS',
          'ROWLEVELCONDITIONS',
          'NATURALLANGUAGE'
        ];
      default:
        return ['NAME', 'VALUE', 'TYPE', 'STATUS'];
    }
  }

  /// Helper method to get dynamic table headers with API data support
  List<String> _getTableHeadersWithApiSupport(String title,
      {ObjectCreationModel? objectData}) {
    // Map title to section name
    final sectionName = _mapTitleToSectionName(title);
    if (sectionName != null) {
      return _getDynamicHeadersForSection(sectionName, objectData);
    }

    // Fallback to static headers
    return _getTableHeaders(title);
  }

  /// Map UI title to API section name
  String? _mapTitleToSectionName(String title) {
    switch (title) {
      case 'Entity Relationships':
        return 'relationships';
      case 'Attribute Business Rules':
        return 'business_rules';
      case 'Enumerated Values':
        return 'enum_values';
      case 'System Permissions':
        return 'system_permissions';
      case 'Security Classification':
        return 'security_classification';
      case 'Role System Permissions':
        return 'role_system_permissions';
      default:
        return null;
    }
  }

  List<double> _getColumnWidths(String title) {
    // Try to use dynamic width calculation if possible
    final sectionName = _mapTitleToSectionName(title);
    if (sectionName != null) {
      final headers = _getTableHeaders(title);
      final objectData = context.read<ObjectCreationProvider>().currentObject;
      final sampleData = _getGenericTableData(sectionName, objectData);

      // Calculate available width (assuming a reasonable table width)
      final screenWidth = MediaQuery.of(context).size.width;
      final availableWidth =
          screenWidth * 0.7; // Use 70% of screen width for table

      try {
        return _getDynamicColumnWidthsForSection(
          sectionName,
          headers,
          availableWidth: availableWidth,
          sampleData: sampleData,
        );
      } catch (e) {
        // Fallback to static widths if dynamic calculation fails
        print('Dynamic width calculation failed for $title: $e');
      }
    }

    // Fallback to static widths for sections not yet migrated or on error
    switch (title) {
      case 'Object Details':
        return [200, 300, 150];
      case 'Entity Relationships':
        return [180, 150, 120, 200, 100];
      case 'Attribute Business Rules':
        return [150, 120, 150, 200, 100];
      case 'Enumerated Values':
        return [150, 120, 100, 120, 150, 80, 80];
      case 'System Permissions':
        return [150, 150, 120, 150, 150, 150, 120, 180, 80, 100];
      case 'Security Classification':
        return [150, 120, 100, 120, 120, 120, 120, 120, 100];
      case 'Role System Permissions':
        return [120, 150, 200, 200, 200];
      default:
        return [200, 200, 100, 100];
    }
  }

  double _getTableMinWidth(String title) {
    List<double> widths = _getColumnWidths(title);
    return widths.reduce((a, b) => a + b);
  }

  List<List<String>> _getTableData(String title) {
    switch (title) {
      case 'Object Details':
        return _buildObjectDetailsTableData();
      default:
        // Return empty list - no fallback static data
        return [];
    }
  }

  /// Build Object Details table data from entity data
  List<List<String>> _buildObjectDetailsTableData() {
    // Get entity data from ObjectCreationProvider
    final objectCreationProvider =
        Provider.of<ObjectCreationProvider>(context, listen: false);

    // Get the first entity if available
    if (objectCreationProvider.hasObjects &&
        objectCreationProvider.objects.isNotEmpty) {
      final entity = objectCreationProvider.objects.first;

      List<List<String>> tableData = [];

      // Entity Name
      if (entity.displayName != null || entity.name != null) {
        tableData.add([
          'Entity Name',
          entity.displayName ?? entity.name ?? '',
          'Extracted'
        ]);
      }

      // Display Name (if different from name)
      if (entity.displayName != null && entity.displayName != entity.name) {
        tableData
            .add(['Display Name', entity.displayName ?? '', 'Auto-generated']);
      }

      // Type
      if (entity.type != null) {
        tableData.add(['Type', _getTypeDisplayValue(entity.type!), 'Inferred']);
      }

      // Business Domain
      if (entity.businessDomain != null) {
        tableData.add(['Business Domain', entity.businessDomain!, 'Extracted']);
      }

      // Business Purpose
      if (entity.businessPurpose != null) {
        tableData
            .add(['Business Purpose', entity.businessPurpose!, 'Generated']);
      }

      // Category
      if (entity.category != null) {
        tableData.add([
          'Category',
          _getCategoryDisplayValue(entity.category!),
          'Inferred'
        ]);
      }

      // Tags
      if (entity.tags != null && entity.tags!.isNotEmpty) {
        tableData.add(['Tags', entity.tags!.join(', '), 'Extracted']);
      }

      // Archival Strategy
      if (entity.archivalStrategy != null) {
        tableData.add([
          'Archival Strategy',
          _getArchivalStrategyDisplayValue(entity.archivalStrategy!),
          'Inferred'
        ]);
      }

      // Color Theme
      if (entity.colorTheme != null) {
        tableData.add([
          'Colour Theme',
          _getColorThemeDisplayValue(entity.colorTheme!),
          'Inferred'
        ]);
      }

      // Icon
      if (entity.icon != null) {
        tableData.add(['Icon', entity.icon!, 'Auto-generated']);
      }

      return tableData;
    }

    // Fallback to static data if no entity data available
    return [
      ['Entity Name', 'Customer', 'Extracted'],
      ['Type', 'Master', 'Inferred'],
      ['Business Domain', 'E-commerce', 'Extracted'],
    ];
  }

  /// Get display value for entity type
  String _getTypeDisplayValue(String type) {
    switch (type.toLowerCase()) {
      case 'master':
        return 'Master';
      case 'transaction':
        return 'Transaction';
      case 'reference':
        return 'Reference';
      default:
        return type.substring(0, 1).toUpperCase() + type.substring(1);
    }
  }

  /// Get display value for category
  String _getCategoryDisplayValue(String category) {
    switch (category.toLowerCase()) {
      case 'core':
        return 'Core';
      case 'supporting':
        return 'Supporting';
      case 'reference':
        return 'Reference';
      case 'user_management':
        return 'User Management';
      default:
        return category
            .replaceAll('_', ' ')
            .split(' ')
            .map((word) =>
                word.substring(0, 1).toUpperCase() + word.substring(1))
            .join(' ');
    }
  }

  /// Get display value for archival strategy
  String _getArchivalStrategyDisplayValue(String strategy) {
    switch (strategy.toLowerCase()) {
      case 'archive_only':
        return 'Archive Only';
      case 'delete_after_archive':
        return 'Delete After Archive';
      case 'permanent_retention':
        return 'Permanent Retention';
      default:
        return strategy
            .replaceAll('_', ' ')
            .split(' ')
            .map((word) =>
                word.substring(0, 1).toUpperCase() + word.substring(1))
            .join(' ');
    }
  }

  /// Get display value for color theme
  String _getColorThemeDisplayValue(String theme) {
    switch (theme.toLowerCase()) {
      case 'indigo':
        return 'Indigo';
      case 'blue':
        return 'Blue';
      case 'orange':
        return 'Orange';
      case 'green':
        return 'Green';
      case 'red':
        return 'Red';
      case 'purple':
        return 'Purple';
      default:
        return theme.substring(0, 1).toUpperCase() + theme.substring(1);
    }
  }

  /// Get background color for source type
  Color _getSourceBackgroundColor(String source) {
    switch (source.toLowerCase()) {
      case 'extracted':
        return const Color(0xFFD1FAE5); // Green background
      case 'auto-generated':
        return const Color(0xFFDBEAFE); // Blue background
      case 'inferred':
        return const Color(0xFFFEF3C7); // Orange/Yellow background
      case 'generated':
        return const Color(0xFFE9D5FF); // Purple background
      default:
        return const Color(0xFFF3F4F6); // Gray background
    }
  }

  /// Get text color for source type
  Color _getSourceTextColor(String source) {
    switch (source.toLowerCase()) {
      case 'extracted':
        return const Color(0xFF065F46); // Green text
      case 'auto-generated':
        return const Color(0xFF1E40AF); // Blue text
      case 'inferred':
        return const Color(0xFF92400E); // Orange text
      case 'generated':
        return const Color(0xFF7C3AED); // Purple text
      default:
        return const Color(0xFF374151); // Gray text
    }
  }

  String _getAddButtonText(String title) {
    switch (title) {
      case 'Object Details':
        return 'Add Property';
      case 'Attributes Details':
        return 'Add Attributes';
      case 'Entity Relationships':
        return 'Add Entity Relationship';
      case 'Attribute Business Rules':
        return 'Add Rule';
      case 'Enumerated Values':
        return 'Add Enum';
      case 'System Permissions':
        return 'Add Permission';
      case 'Security Classification':
        return 'Add Classification';
      default:
        return 'Add Item';
    }
  }

  void _showAddModal(BuildContext context, String title) {
    // Use specialized dialogs for specific sections
    switch (title) {
      case 'Security Classification':
        _showEnhancedSecurityClassificationDialog(
          context: context,
          title: _getModalTitle(title),
          isEditMode: false,
        );
        break;
      case 'System Permissions':
        _showEnhancedSystemPermissionsDialog(
          context: context,
          title: _getModalTitle(title),
          isEditMode: false,
        );
        break;
      case 'Enumerated Values':
        _showEnhancedEnumeratedValuesDialog(
          context: context,
          title: _getModalTitle(title),
          isEditMode: false,
        );
        break;
      default:
        _showEnhancedDialog(
          context: context,
          title: _getModalTitle(title),
          isEditMode: false,
          sectionTitle: title,
        );
        break;
    }
  }

  String _getModalTitle(String title) {
    switch (title) {
      case 'Object Details':
        return 'Add Object Property';
      case 'Entity Relationships':
        return 'Add Entity Relationship';
      case 'Attribute Business Rules':
        return 'Add Business Rule';
      case 'Enumerated Values':
        return 'Add Enumerated Value';
      case 'System Permissions':
        return 'Add System Permission';
      case 'Security Classification':
        return 'Add Security Classification';
      default:
        return 'Add Configuration';
    }
  }

  List<Widget> _buildModalFormFields(BuildContext context, String title) {
    switch (title) {
      case 'Object Details':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Property Name', 'property_name')),
              const SizedBox(width: 24),
              Expanded(
                  child:
                      _buildModalFormField(context, 'Value', 'Property Value')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Type',
                      ['string', 'number', 'boolean', 'date'])),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Required', ['No', 'Yes'])),
            ],
          ),
          const SizedBox(height: 20),
          _buildModalFormField(context, 'Description', 'Property description'),
        ];
      case 'Entity Relationships':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Relationship Name', 'relationship_name')),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormField(
                      context, 'Target Entity', 'Target Entity')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Type',
                      ['One-to-One', 'One-to-Many', 'Many-to-Many'])),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Cardinality', ['1:1', '1:N', 'N:M'])),
            ],
          ),
          const SizedBox(height: 20),
          _buildModalDropdownField(
              context, 'Status', ['Active', 'Pending', 'Inactive']),
        ];
      default:
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'Name', 'Enter name')),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormField(context, 'Value', 'Enter value')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Type', ['string', 'number', 'boolean'])),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Status', ['Active', 'Inactive'])),
            ],
          ),
        ];
    }
  }

  String _getPlaceholderTitle(String title) {
    switch (title) {
      case 'Object Details':
        return 'Object Details Configuration';
      case 'Entity Relationships':
        return 'Entity Relationships Configuration';
      case 'Attribute Business Rules':
        return 'Business Rules Configuration';
      case 'Enumerated Values':
        return 'Enumerated Values Configuration';
      case 'System Permissions':
        return 'System Permissions Configuration';
      case 'Security Classification':
        return 'Security Classification Configuration';
      default:
        return '$title Configuration';
    }
  }

  String _getPlaceholderDescription(String title) {
    switch (title) {
      case 'Object Details':
        return 'Configure detailed information about the object including its properties, metadata, and general settings.';
      case 'Entity Relationships':
        return 'Define relationships between this entity and other entities in your system.';
      case 'Attribute Business Rules':
        return 'Set up business rules and validation logic for the attributes of this entity.';
      case 'Enumerated Values':
        return 'Configure predefined values and options for enumerated fields.';
      case 'System Permissions':
        return 'Define access control and permission settings for this entity.';
      case 'Security Classification':
        return 'Set security levels and classification rules for data protection.';
      default:
        return 'Configure settings and options for this section.';
    }
  }

  // Helper method to get status information based on item title
  Map<String, dynamic> _getStatusInfo(String title) {
    switch (title) {
      case 'Object Details':
        return {
          'status': 'Partial Completion',
          'count': '1 Entity Detected',
          'backgroundColor': Color(0xFFFEF3C7),
          'textColor': Color(0xFF92400E),
        };
      case 'Properties & Methods':
        return {
          'status': 'Completed',
          'count': '25 Attributes',
          'backgroundColor': Color(0xFFD1FAE5),
          'textColor': Color(0xFF065F46),
        };
      default:
        return {
          'status': 'Missing',
          'count': '0 Configure',
          'backgroundColor': Color(0xFFFEE2E2),
          'textColor': Color(0xFF991B1B),
        };
    }
  }

  double _getFormTableMinWidth(String title) {
    switch (title) {
      case 'Object Details':
        return 800; // 5 columns + actions
      case 'Entity Relationships':
        return 880; // 5 columns + actions - increased to prevent overlap
      case 'Attribute Business Rules':
        return 750; // 5 columns + actions
      case 'Enumerated Values':
        return 850; // 5 columns + actions
      case 'System Permissions':
        return 750; // 5 columns + actions
      case 'Security Classification':
        return 800; // 5 columns + actions
      default:
        return 700;
    }
  }

  Widget _buildFormTableHeader(BuildContext context, String title) {
    List<String> headers = _getFormTableHeaders(title);
    List<double> widths = _getFormColumnWidths(title);

    return Row(
      children: headers.asMap().entries.map((entry) {
        int index = entry.key;
        String header = entry.value;
        double width = widths[index];

        return SizedBox(
          width: width,
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              header,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.labelSmall(context),
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey[700],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildScrollableFormTableHeader(BuildContext context, String title) {
    List<String> headers =
        _getTableHeaders(title); // Get headers without ACTIONS
    List<double> widths =
        _getColumnWidths(title); // Get widths without actions column

    return Row(
      children: headers.asMap().entries.map((entry) {
        int index = entry.key;
        String header = entry.value;
        double width = widths[index];

        return SizedBox(
          width: width,
          child: Align(
            alignment: Alignment.centerLeft,
            child: Tooltip(
              message: header, // full text displayed on hover
              child: Text(
                header,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[700],
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  List<Widget> _buildFormTableRowsWithFixedActions(
      BuildContext context, String title) {
    List<List<String>> rowsData = _getTableData(title);

    return rowsData.asMap().entries.map((entry) {
      int index = entry.key;
      List<String> rowData = entry.value;
      return _buildGenericDisplayRowWithFixedActions(
          context, index, title, rowData);
    }).toList();
  }

  Widget _buildGenericDisplayRowWithFixedActions(
      BuildContext context, int index, String title, List<String> rowData) {
    List<double> widths =
        _getColumnWidths(title); // Get widths without actions column

    return Container(
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: [
          // Scrollable data columns
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                child: Row(
                  children: rowData.asMap().entries.map((entry) {
                    int colIndex = entry.key;
                    String data = entry.value;
                    double width = widths[colIndex];

                    return SizedBox(
                      width: width,
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          data,
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.titleSmall(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
          // Fixed Actions column
          Container(
            width: 120,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            decoration: const BoxDecoration(
              border: Border(
                left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                IconButton(
                  onPressed: () =>
                      _showEditModal(context, index, title, rowData),
                  icon: const Icon(Icons.edit_outlined),
                  color: Colors.blue[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Edit',
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () => _showDeleteConfirmationGeneric(
                      context, index, title, rowData[0]),
                  icon: const Icon(Icons.delete_outline),
                  color: Colors.red[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Delete',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildFormTableRows(BuildContext context, String title) {
    List<List<String>> rowsData = _getTableData(title);

    return rowsData.asMap().entries.map((entry) {
      int index = entry.key;
      List<String> rowData = entry.value;
      return _buildGenericDisplayRow(context, index, title, rowData);
    }).toList();
  }

  Widget _buildGenericDisplayRow(
      BuildContext context, int index, String title, List<String> rowData) {
    List<double> widths = _getFormColumnWidths(title);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Build display fields based on column type
          ...rowData.asMap().entries.map((entry) {
            int colIndex = entry.key;
            String data = entry.value;
            double width = widths[colIndex];

            return SizedBox(
              width: width,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  data,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            );
          }),

          // Actions
          SizedBox(
            width: 120,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  IconButton(
                    onPressed: () =>
                        _showEditModal(context, index, title, rowData),
                    icon: const Icon(Icons.edit_outlined),
                    color: Colors.blue[600],
                    iconSize: 18,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    tooltip: 'Edit',
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: () => _showDeleteConfirmationGeneric(
                        context, index, title, rowData[0]),
                    icon: const Icon(Icons.delete_outline),
                    color: Colors.red[600],
                    iconSize: 18,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    tooltip: 'Delete',
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormFieldByType(
      BuildContext context, String title, int columnIndex, String data) {
    List<String> dropdownColumns = _getDropdownColumns(title);
    List<String> headers = _getFormTableHeaders(title);

    if (columnIndex < headers.length &&
        dropdownColumns.contains(headers[columnIndex])) {
      // Build dropdown with fixed height and 4px margin
      List<String> options = _getDropdownOptions(title, headers[columnIndex]);
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        child: SizedBox(
          height: 33, // Fixed height to match TextFormField
          child: DropdownButtonFormField<String>(
            value: options.contains(data) ? data : options.first,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(4),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(4),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(4),
                borderSide: const BorderSide(color: Color(0xFF0058FF)),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              isDense: true,
            ),
            icon: Container(
              alignment: Alignment.centerRight,
              child: const Icon(
                Icons.keyboard_arrow_down,
                size: 20,
              ),
            ),
            iconSize: 20,
            items: options.map((String value) {
              return DropdownMenuItem<String>(
                value: value,
                child: Text(
                  value,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
              );
            }).toList(),
            onChanged: (String? newValue) {
              // Handle change
            },
          ),
        ),
      );
    } else {
      // Build text field with 4px margin
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        child: TextFormField(
          initialValue: data,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
            isDense: true,
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodySmall(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      );
    }
  }

  List<String> _getFormTableHeaders(String title) {
    List<String> baseHeaders = _getTableHeaders(title);
    return [...baseHeaders, 'ACTIONS'];
  }

  List<double> _getFormColumnWidths(String title) {
    List<double> baseWidths = _getColumnWidths(title);
    return [...baseWidths, 120]; // Add 120px for actions column
  }

  List<String> _getDropdownColumns(String title) {
    switch (title) {
      case 'Object Details':
        return ['TYPE', 'REQUIRED'];
      case 'Entity Relationships':
        return ['TYPE', 'CARDINALITY', 'STATUS'];
      case 'Attribute Business Rules':
        return ['ACTION', 'PRIORITY'];
      case 'Enumerated Values':
        return ['STATUS'];
      case 'System Permissions':
        return ['ROLE', 'ACCESS LEVEL', 'STATUS'];
      case 'Security Classification':
        return ['LEVEL', 'STATUS'];
      default:
        return ['TYPE', 'STATUS'];
    }
  }

  List<String> _getDropdownOptions(String title, String column) {
    switch (column) {
      case 'TYPE':
        if (title == 'Object Details') {
          return ['string', 'number', 'boolean', 'date'];
        } else if (title == 'Entity Relationships') {
          return ['One-to-One', 'One-to-Many', 'Many-to-Many'];
        }
        return ['string', 'number', 'boolean'];
      case 'REQUIRED':
        return ['YES', 'NO'];
      case 'CARDINALITY':
        return ['1:1', '1:N', 'N:M'];
      case 'STATUS':
        return ['Active', 'Pending', 'Inactive'];
      case 'ACTION':
        return ['reject', 'warn', 'format', 'accept'];
      case 'PRIORITY':
        return ['Low', 'Medium', 'High'];
      case 'ROLE':
        return ['User', 'Admin', 'SuperAdmin'];
      case 'ACCESS LEVEL':
        return ['Read', 'Write', 'Delete'];
      case 'LEVEL':
        return ['Low', 'Medium', 'High', 'Critical'];
      default:
        return ['Active', 'Inactive'];
    }
  }

  void _showEditModal(
      BuildContext context, int index, String title, List<String> rowData) {
    // Use specialized dialogs for specific sections
    switch (title) {
      case 'Security Classification':
        // Use class-based data directly from the list
        if (index < _securityClassificationData.length) {
          final securityClassification = _securityClassificationData[index];
          _showEnhancedSecurityClassificationDialog(
            context: context,
            title: 'Edit ${_getModalTitle(title).replaceFirst('Add ', '')}',
            isEditMode: true,
            editData: securityClassification,
            editIndex: index,
          );
        }
        break;
      case 'System Permissions':
        // Use class-based data directly from the list
        if (index < _systemPermissionData.length) {
          final systemPermission = _systemPermissionData[index];
          _showEnhancedSystemPermissionsDialog(
            context: context,
            title: 'Edit ${_getModalTitle(title).replaceFirst('Add ', '')}',
            isEditMode: true,
            editData: systemPermission,
            editIndex: index,
          );
        }
        break;
      case 'Enumerated Values':
        // Use class-based data directly from the list
        if (index < _enumValueData.length) {
          final enumValue = _enumValueData[index];
          _showEnhancedEnumeratedValuesDialog(
            context: context,
            title: 'Edit ${_getModalTitle(title).replaceFirst('Add ', '')}',
            isEditMode: true,
            editData: enumValue,
            editIndex: index,
          );
        }
        break;
      default:
        _showEnhancedDialog(
          context: context,
          title: 'Edit ${_getModalTitle(title).replaceFirst('Add ', '')}',
          isEditMode: true,
          sectionTitle: title,
          editIndex: index,
          editData: rowData,
        );
        break;
    }
  }

  /// Enhanced dialog with scrolling support and comprehensive field mapping
  void _showEnhancedDialog({
    required BuildContext context,
    required String title,
    required bool isEditMode,
    required String sectionTitle,
    int? editIndex,
    List<String>? editData,
  }) {
    // Create controllers for all possible fields
    final Map<String, TextEditingController> controllers = {};
    final Map<String, String> dropdownValues = {};

    // Initialize controllers and values based on section
    _initializeDialogFields(
        sectionTitle, controllers, dropdownValues, editData);

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Container(
                width: math.min(MediaQuery.of(context).size.width * 0.8, 534),
                height: math.min(MediaQuery.of(context).size.height * 0.8, 600),
                child: Column(
                  children: [
                    // Header with bottom border
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: const BoxDecoration(
                        border: Border(
                          bottom:
                              BorderSide(color: Color(0xFFE5E7EB), width: 1),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            title,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.titleLarge(context),
                              fontWeight: FontWeight.w600,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                          IconButton(
                            onPressed: () => Navigator.of(context).pop(),
                            icon: const Icon(Icons.close),
                            iconSize: 24,
                            color: Colors.grey[600],
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                        ],
                      ),
                    ),

                    // Scrollable content area
                    Expanded(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(24),
                        child: _buildEnhancedFormFields(context, sectionTitle,
                            controllers, dropdownValues, setState),
                      ),
                    ),

                    // Footer with top border
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: const BoxDecoration(
                        border: Border(
                          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                                side: BorderSide(color: Colors.grey[300]!),
                              ),
                            ),
                            child: Text(
                              'Cancel',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.grey[700],
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          ElevatedButton(
                            onPressed: () {
                              _handleDialogSubmit(
                                context,
                                sectionTitle,
                                controllers,
                                dropdownValues,
                                isEditMode,
                                editIndex,
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF0058FF),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              elevation: 0,
                            ),
                            child: Text(
                              isEditMode ? 'Update' : 'Apply This',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// Initialize dialog fields based on section type
  void _initializeDialogFields(
    String sectionTitle,
    Map<String, TextEditingController> controllers,
    Map<String, String> dropdownValues,
    List<String>? editData,
  ) {
    final headers = _getTableHeaders(sectionTitle);
    final headerToKeyMap = _getHeaderToKeyMapping();

    for (int i = 0; i < headers.length; i++) {
      final header = headers[i];
      final key = headerToKeyMap[header] ?? header.toLowerCase();
      final value = editData != null && i < editData.length ? editData[i] : '';

      // Determine if this field should be a dropdown
      if (_isDropdownField(sectionTitle, header)) {
        dropdownValues[key] = value.isNotEmpty
            ? value
            : _getDefaultDropdownValue(sectionTitle, header);
      } else {
        controllers[key] = TextEditingController(text: value);
      }
    }
  }

  /// Build enhanced form fields with proper layout
  Widget _buildEnhancedFormFields(
    BuildContext context,
    String sectionTitle,
    Map<String, TextEditingController> controllers,
    Map<String, String> dropdownValues,
    StateSetter setState,
  ) {
    final headers = _getTableHeaders(sectionTitle);
    final headerToKeyMap = _getHeaderToKeyMapping();
    final widgets = <Widget>[];

    // Build fields in pairs for better layout
    for (int i = 0; i < headers.length; i += 2) {
      final leftHeader = headers[i];
      final leftKey = headerToKeyMap[leftHeader] ?? leftHeader.toLowerCase();

      Widget leftField = _buildDialogField(context, leftHeader, leftKey,
          sectionTitle, controllers, dropdownValues, setState);

      Widget? rightField;
      if (i + 1 < headers.length) {
        final rightHeader = headers[i + 1];
        final rightKey =
            headerToKeyMap[rightHeader] ?? rightHeader.toLowerCase();
        rightField = _buildDialogField(context, rightHeader, rightKey,
            sectionTitle, controllers, dropdownValues, setState);
      }

      if (rightField != null) {
        widgets.add(
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(child: leftField),
              const SizedBox(width: 24),
              Expanded(child: rightField),
            ],
          ),
        );
      } else {
        widgets.add(leftField);
      }

      if (i + 2 < headers.length) {
        widgets.add(const SizedBox(height: 20));
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets,
    );
  }

  /// Build individual dialog field (text or dropdown)
  Widget _buildDialogField(
    BuildContext context,
    String header,
    String key,
    String sectionTitle,
    Map<String, TextEditingController> controllers,
    Map<String, String> dropdownValues,
    StateSetter setState,
  ) {
    final displayLabel = _formatHeaderForDisplay(header);

    if (_isDropdownField(sectionTitle, header)) {
      final options = _getDropdownOptionsForField(sectionTitle, header);
      final currentValue = dropdownValues[key] ?? options.first;

      return _buildEditableDropdownField(
        context,
        displayLabel,
        options,
        currentValue,
        (value) {
          setState(() {
            dropdownValues[key] = value!;
          });
        },
      );
    } else {
      return _buildEditableFormField(
        context,
        displayLabel,
        controllers[key]!,
      );
    }
  }

  /// Handle dialog form submission
  void _handleDialogSubmit(
    BuildContext context,
    String sectionTitle,
    Map<String, TextEditingController> controllers,
    Map<String, String> dropdownValues,
    bool isEditMode,
    int? editIndex,
  ) {
    // Validate required fields
    if (!_validateDialogFields(sectionTitle, controllers, dropdownValues)) {
      return;
    }

    // Create data map from form values
    final data = <String, String>{};
    controllers.forEach((key, controller) {
      data[key] = controller.text;
    });
    dropdownValues.forEach((key, value) {
      data[key] = value;
    });

    // Update the appropriate data structure
    setState(() {
      if (isEditMode && editIndex != null) {
        _updateTableData(sectionTitle, editIndex, data);
      } else {
        _addTableData(sectionTitle, data);
      }
    });

    Navigator.of(context).pop();
  }

  /// Check if a field should be a dropdown
  bool _isDropdownField(String sectionTitle, String header) {
    final dropdownFields = {
      'Object Details': ['TYPE', 'REQUIRED'],
      'Entity Relationships': ['RELATIONSHIPTYPE', 'STATUS'],
      'Attribute Business Rules': ['OPERATOR', 'STATUS'],
      'Enumerated Values': ['ACTIVE'],
      'System Permissions': ['PERMISSIONTYPE', 'STATUS'],
      'Security Classification': [
        'CLASSIFICATION',
        'PIITYPE',
        'ENCRYPTIONREQUIRED',
        'ENCRYPTIONTYPE',
        'MASKINGREQUIRED',
        'ACCESSLEVEL',
        'AUDITTRAIL'
      ],
    };

    return dropdownFields[sectionTitle]?.contains(header) ?? false;
  }

  /// Get default dropdown value for a field
  String _getDefaultDropdownValue(String sectionTitle, String header) {
    final defaults = {
      'TYPE': 'string',
      'REQUIRED': 'NO',
      'RELATIONSHIPTYPE': 'One-to-Many',
      'STATUS': 'Active',
      'OPERATOR': 'equals',
      'ACTIVE': 'Yes',
      'PERMISSIONTYPE': 'entity',
      'CLASSIFICATION': 'internal',
      'PIITYPE': 'none',
      'ENCRYPTIONREQUIRED': 'No',
      'ENCRYPTIONTYPE': 'none',
      'MASKINGREQUIRED': 'No',
      'ACCESSLEVEL': 'read_internal',
      'AUDITTRAIL': 'Yes',
    };

    return defaults[header] ?? 'Active';
  }

  /// Get dropdown options for a specific field
  List<String> _getDropdownOptionsForField(String sectionTitle, String header) {
    switch (header) {
      case 'TYPE':
        return ['string', 'number', 'boolean', 'date'];
      case 'REQUIRED':
        return ['NO', 'YES'];
      case 'RELATIONSHIPTYPE':
        return ['One-to-One', 'One-to-Many', 'Many-to-Many'];
      case 'STATUS':
        return ['Active', 'Pending', 'Inactive'];
      case 'OPERATOR':
        return [
          'equals',
          'not_equals',
          'greater_than',
          'less_than',
          'contains',
          'regex'
        ];
      case 'ACTIVE':
        return ['Yes', 'No'];
      case 'PERMISSIONTYPE':
        return ['entity', 'field', 'action', 'system'];
      case 'CLASSIFICATION':
        return ['public', 'internal', 'confidential', 'restricted'];
      case 'PIITYPE':
        return ['none', 'email', 'phone', 'ssn', 'address', 'name'];
      case 'ENCRYPTIONREQUIRED':
      case 'MASKINGREQUIRED':
      case 'AUDITTRAIL':
        return ['Yes', 'No'];
      case 'ENCRYPTIONTYPE':
        return ['none', 'aes256', 'rsa', 'hash'];
      case 'ACCESSLEVEL':
        return [
          'read_public',
          'read_internal',
          'read_restricted',
          'write_internal',
          'write_restricted'
        ];
      default:
        return ['Active', 'Inactive'];
    }
  }

  /// Format header for display
  String _formatHeaderForDisplay(String header) {
    // Convert from uppercase to title case with spaces
    return header
        .toLowerCase()
        .split('_')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ')
        .replaceAll('id', 'ID')
        .replaceAll('pii', 'PII')
        .replaceAll('api', 'API');
  }

  /// Validate dialog fields
  bool _validateDialogFields(
    String sectionTitle,
    Map<String, TextEditingController> controllers,
    Map<String, String> dropdownValues,
  ) {
    // Basic validation - check if required fields are filled
    final requiredFields = _getRequiredFields(sectionTitle);

    for (final field in requiredFields) {
      if (controllers.containsKey(field)) {
        if (controllers[field]!.text.trim().isEmpty) {
          return false;
        }
      }
    }

    return true;
  }

  /// Get required fields for a section
  List<String> _getRequiredFields(String sectionTitle) {
    switch (sectionTitle) {
      case 'Object Details':
        return ['property name', 'value'];
      case 'Entity Relationships':
        return ['relatedentity', 'relationshiptype'];
      case 'Attribute Business Rules':
        return ['attributename', 'operator'];
      case 'Enumerated Values':
        return ['entityattribute', 'enumname', 'value'];
      case 'System Permissions':
        return ['permissionid', 'permissionname'];
      case 'Security Classification':
        return ['entityattribute', 'classification'];
      default:
        return [];
    }
  }

  /// Add new data to table
  void _addTableData(String sectionTitle, Map<String, String> data) {
    switch (sectionTitle) {
      case 'Object Details':
        // Add to object details data structure
        break;
      case 'Entity Relationships':
        if (!_tableData.containsKey('Entity Relationships')) {
          _tableData['Entity Relationships'] = [];
        }
        _tableData['Entity Relationships']!.add(data);
        // Also add to the _relationshipData list
        _relationshipData.add(EntityRelationship(
          primaryKey: data['primaryKey'],
          foreignKey: data['foreignKey'],
          relatedEntity: data['relatedEntity'],
          relationshipType: data['relationshipType'],
          onDelete: data['onDelete'],
          onUpdate: data['onUpdate'],
          foreignKeyType: data['foreignKeyType'],
          description: data['description'],
        ));
        break;
      case 'Attribute Business Rules':
        if (!_tableData.containsKey('Attribute Business Rules')) {
          _tableData['Attribute Business Rules'] = [];
        }
        _tableData['Attribute Business Rules']!.add(data);
        break;
      case 'Enumerated Values':
        if (!_tableData.containsKey('Enumerated Values')) {
          _tableData['Enumerated Values'] = [];
        }
        _tableData['Enumerated Values']!.add(data);
        break;
      case 'System Permissions':
        if (!_tableData.containsKey('System Permissions')) {
          _tableData['System Permissions'] = [];
        }
        _tableData['System Permissions']!.add(data);
        break;
      case 'Security Classification':
        if (!_tableData.containsKey('Security Classification')) {
          _tableData['Security Classification'] = [];
        }
        _tableData['Security Classification']!.add(data);
        break;
    }
  }

  /// Update existing table data
  void _updateTableData(
      String sectionTitle, int index, Map<String, String> data) {
    switch (sectionTitle) {
      case 'Object Details':
        // Update object details data structure
        break;
      case 'Entity Relationships':
        if (_tableData.containsKey('Entity Relationships') &&
            index < _tableData['Entity Relationships']!.length) {
          _tableData['Entity Relationships']![index] = data;
        }
        // Also update the _relationshipData list
        if (index < _relationshipData.length) {
          _relationshipData[index] = EntityRelationship(
            primaryKey: data['primaryKey'],
            foreignKey: data['foreignKey'],
            relatedEntity: data['relatedEntity'],
            relationshipType: data['relationshipType'],
            onDelete: data['onDelete'],
            onUpdate: data['onUpdate'],
            foreignKeyType: data['foreignKeyType'],
            description: data['description'],
          );
        }
        break;
      case 'Attribute Business Rules':
        if (_tableData.containsKey('Attribute Business Rules') &&
            index < _tableData['Attribute Business Rules']!.length) {
          _tableData['Attribute Business Rules']![index] = data;
        }
        break;
      case 'Enumerated Values':
        if (_tableData.containsKey('Enumerated Values') &&
            index < _tableData['Enumerated Values']!.length) {
          _tableData['Enumerated Values']![index] = data;
        }
        break;
      case 'System Permissions':
        if (_tableData.containsKey('System Permissions') &&
            index < _tableData['System Permissions']!.length) {
          _tableData['System Permissions']![index] = data;
        }
        break;
      case 'Security Classification':
        if (_tableData.containsKey('Security Classification') &&
            index < _tableData['Security Classification']!.length) {
          _tableData['Security Classification']![index] = data;
        }
        break;
    }
  }

  void _showDeleteConfirmationGeneric(
      BuildContext context, int index, String title, String itemName) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: Text(
            'Delete ${_getSingularTitle(title)}',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Text(
            'Are you sure you want to delete "$itemName"? This action cannot be undone.',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[700],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[700],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                // Handle delete logic here
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[600],
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Delete',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Helper method to safely get row data with optional default value
  String _safeGetRowData(List<String> rowData, int index,
      [String defaultValue = '']) {
    if (index < 0 || index >= rowData.length) {
      return defaultValue;
    }
    return rowData[index].isEmpty ? defaultValue : rowData[index];
  }

  List<Widget> _buildModalFormFieldsWithValues(
      BuildContext context, String title, List<String> rowData) {
    switch (title) {
      case 'Object Details':
        // Headers: ['PROPERTY NAME', 'VALUE', 'TYPE', 'REQUIRED', 'DESCRIPTION']
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Property Name', _safeGetRowData(rowData, 0))),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Value', _safeGetRowData(rowData, 1))),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'Type',
                      ['string', 'number', 'boolean', 'date', 'email'],
                      _safeGetRowData(rowData, 2, 'string'))),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownFieldWithValue(context, 'Required',
                      ['YES', 'NO'], _safeGetRowData(rowData, 3, 'NO'))),
            ],
          ),
          const SizedBox(height: 20),
          _buildModalFormFieldWithValue(
              context, 'Description', _safeGetRowData(rowData, 4)),
        ];
      case 'Entity Relationships':
        // Headers: ['RELATEDENTITY', 'RELATIONSHIPTYPE', 'FOREIGNKEY', 'DESCRIPTION', 'STATUS']
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Related Entity', _safeGetRowData(rowData, 0))),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'Relationship Type',
                      ['One-to-One', 'One-to-Many', 'Many-to-Many'],
                      _safeGetRowData(rowData, 1, 'One-to-Many'))),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Foreign Key', _safeGetRowData(rowData, 2))),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'Status',
                      ['Active', 'Pending', 'Inactive'],
                      _safeGetRowData(rowData, 4, 'Active'))),
            ],
          ),
          const SizedBox(height: 20),
          _buildModalFormFieldWithValue(
              context, 'Description', _safeGetRowData(rowData, 3)),
        ];
      case 'Attribute Business Rules':
        // Headers: ['ATTRIBUTENAME', 'OPERATOR', 'VALUE', 'ERRORMESSAGE', 'STATUS']
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Attribute Name', _safeGetRowData(rowData, 0))),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'Operator',
                      [
                        'equals',
                        'not_equals',
                        'greater_than',
                        'less_than',
                        'contains',
                        'regex'
                      ],
                      _safeGetRowData(rowData, 1, 'equals'))),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Value', _safeGetRowData(rowData, 2))),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'Status',
                      ['Active', 'Inactive'],
                      _safeGetRowData(rowData, 4, 'Active'))),
            ],
          ),
          const SizedBox(height: 20),
          _buildModalFormFieldWithValue(
              context, 'Error Message', _safeGetRowData(rowData, 3)),
        ];
      case 'Enumerated Values':
        // Headers: ['ENTITYATTRIBUTE', 'ENUMNAME', 'VALUE', 'DISPLAY', 'DESCRIPTION', 'SORTORDER', 'ACTIVE']
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(context,
                      'Entity Attribute', _safeGetRowData(rowData, 0))),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Enum Name', _safeGetRowData(rowData, 1))),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Value', _safeGetRowData(rowData, 2))),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Display', _safeGetRowData(rowData, 3))),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Description', _safeGetRowData(rowData, 4))),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Sort Order', _safeGetRowData(rowData, 5, '0'))),
            ],
          ),
          const SizedBox(height: 20),
          _buildModalDropdownFieldWithValue(context, 'Active',
              ['true', 'false'], _safeGetRowData(rowData, 6, 'true')),
        ];
      case 'System Permissions':
        // Headers: ['PERMISSIONID', 'PERMISSIONNAME', 'PERMISSIONTYPE', 'RESOURCEIDENTIFIER', 'ACTIONS', 'DESCRIPTION', 'SCOPE', 'NATURALLANGUAGE', 'VERSION', 'STATUS']
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Permission ID', _safeGetRowData(rowData, 0))),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Permission Name', _safeGetRowData(rowData, 1))),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'Permission Type',
                      ['entity', 'action', 'resource', 'system'],
                      _safeGetRowData(rowData, 2, 'entity'))),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormFieldWithValue(context,
                      'Resource Identifier', _safeGetRowData(rowData, 3))),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Actions', _safeGetRowData(rowData, 4))),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Scope', _safeGetRowData(rowData, 6))),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Description', _safeGetRowData(rowData, 5))),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Version', _safeGetRowData(rowData, 8, '1'))),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(context,
                      'Natural Language', _safeGetRowData(rowData, 7))),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'Status',
                      ['active', 'inactive'],
                      _safeGetRowData(rowData, 9, 'active'))),
            ],
          ),
        ];
      case 'Security Classification':
        // Headers: ['ENTITYATTRIBUTE', 'CLASSIFICATION', 'PIITYPE', 'ENCRYPTIONREQUIRED', 'ENCRYPTIONTYPE', 'MASKINGREQUIRED', 'MASKINGPATTERN', 'ACCESSLEVEL', 'AUDITTRAIL']
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(context,
                      'Entity Attribute', _safeGetRowData(rowData, 0))),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'Classification',
                      ['Public', 'Internal', 'Confidential', 'Restricted'],
                      _safeGetRowData(rowData, 1, 'Internal'))),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'PII Type',
                      ['None', 'Personal', 'Sensitive', 'Financial'],
                      _safeGetRowData(rowData, 2, 'None'))),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'Encryption Required',
                      ['true', 'false'],
                      _safeGetRowData(rowData, 3, 'false'))),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'Encryption Type',
                      ['AES-256', 'RSA', 'None'],
                      _safeGetRowData(rowData, 4, 'None'))),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'Masking Required',
                      ['true', 'false'],
                      _safeGetRowData(rowData, 5, 'false'))),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Masking Pattern', _safeGetRowData(rowData, 6))),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'Access Level',
                      ['Public', 'User', 'Admin', 'System'],
                      _safeGetRowData(rowData, 7, 'User'))),
            ],
          ),
          const SizedBox(height: 20),
          _buildModalDropdownFieldWithValue(context, 'Audit Trail',
              ['true', 'false'], _safeGetRowData(rowData, 8, 'true')),
        ];
      case 'Role System Permissions':
        // Headers: ['ROLEID', 'PERMISSIONID', 'GRANTEDACTIONS', 'ROWLEVELCONDITIONS', 'NATURALLANGUAGE']
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Role ID', _safeGetRowData(rowData, 0))),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Permission ID', _safeGetRowData(rowData, 1))),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Granted Actions', _safeGetRowData(rowData, 2))),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormFieldWithValue(context,
                      'Row Level Conditions', _safeGetRowData(rowData, 3))),
            ],
          ),
          const SizedBox(height: 20),
          _buildModalFormFieldWithValue(
              context, 'Natural Language', _safeGetRowData(rowData, 4)),
        ];
      default:
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Name', _safeGetRowData(rowData, 0))),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Value', _safeGetRowData(rowData, 1))),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'Type',
                      ['string', 'number', 'boolean'],
                      _safeGetRowData(rowData, 2, 'string'))),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'Status',
                      ['Active', 'Inactive'],
                      _safeGetRowData(rowData, 3, 'Active'))),
            ],
          ),
        ];
    }
  }

  String _getSingularTitle(String title) {
    switch (title) {
      case 'Entity Relationships':
        return 'Relationship';
      case 'Object Details':
        return 'Property';
      case 'Attribute Business Rules':
        return 'Rule';
      case 'Enumerated Values':
        return 'Enum';
      case 'System Permissions':
        return 'Permission';
      case 'Security Classification':
        return 'Classification';
      default:
        return 'Item';
    }
  }

  /// Build table cell content with special handling for source badges
  Widget _buildTableCellContent(
      BuildContext context, String title, int colIndex, String data) {
    // For Object Details, the third column (index 2) is the SOURCE column
    if (title == 'Object Details' && colIndex == 2) {
      return _buildSourceBadge(data);
    }

    // For other columns, return regular text
    return Text(
      data,
      style: FontManager.getCustomStyle(
        fontSize: ResponsiveFontSizes.labelMedium(context),
        fontWeight: FontWeight.w400,
        fontFamily: FontManager.fontFamilyTiemposText,
        color: Colors.black,
      ),
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
    );
  }

  /// Build colored badge for source information
  Widget _buildSourceBadge(String source) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getSourceBackgroundColor(source),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        source,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: _getSourceTextColor(source),
          fontFamily: FontManager.fontFamilyTiemposText,
        ),
      ),
    );
  }

  List<Widget> _buildScrollableFormTableRows(BuildContext context, String title,
      {ObjectCreationModel? objectData}) {
    List<List<String>> rowsData =
        _getTableDataWithApiSupport(title, objectData: objectData);
    List<double> widths = _getColumnWidths(title);

    return rowsData.map((rowData) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: const BoxDecoration(
          border: Border(
            top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
          ),
        ),
        child: Row(
          children: rowData.asMap().entries.map((entry) {
            int colIndex = entry.key;
            String data = entry.value;
            double width = widths[colIndex];

            return SizedBox(
              width: width,
              child: Align(
                alignment: Alignment.centerLeft,
                child: _buildTableCellContent(context, title, colIndex, data),
              ),
            );
          }).toList(),
        ),
      );
    }).toList();
  }

  List<Widget> _buildFixedActionsColumn(BuildContext context, String title,
      {ObjectCreationModel? objectData}) {
    List<List<String>> rowsData =
        _getTableDataWithApiSupport(title, objectData: objectData);

    return rowsData.asMap().entries.map((entry) {
      int index = entry.key;
      List<String> rowData = entry.value;

      return IgnorePointer(
        ignoring: !Provider.of<WebHomeProviderStatic>(context).isAIMode,
        child: Opacity(
          opacity:
              !Provider.of<WebHomeProviderStatic>(context).isAIMode ? 0.5 : 1.0,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 8),
            decoration: const BoxDecoration(
              border: Border(
                top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  onPressed: () =>
                      _showEditModal(context, index, title, rowData),
                  icon: const Icon(Icons.edit_outlined),
                  color: Colors.blue[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Edit',
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () => _showDeleteConfirmationGeneric(
                      context, index, title, rowData[0]),
                  icon: const Icon(Icons.delete_outline),
                  color: Colors.red[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Delete',
                ),
              ],
            ),
          ),
        ),
      );
    }).toList();
  }

  Widget _buildAttributeScrollableRow(
    BuildContext context,
    int index,
    String attributeName,
    String displayName,
    String dataType,
    String required,
    String unique,
    String description,
    String defaultValue,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: [
          // Attribute Name
          SizedBox(
            width: 200,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                attributeName,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ),

          // Display Name
          SizedBox(
            width: 200,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                displayName,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ),

          // Data Type
          SizedBox(
            width: 120,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                dataType,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ),

          // Required
          SizedBox(
            width: 110,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                required,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ),

          // Unique
          SizedBox(
            width: 110,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                unique,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ),

          // Description
          SizedBox(
            width: 250,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                description,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ),

          // Default Value
          SizedBox(
            width: 150,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                defaultValue,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttributeFixedActionsRow(
    BuildContext context,
    int index,
    String attributeName,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          IconButton(
            onPressed: () {
              // Find the attribute data for editing
              final attributeData = _attributeData[index];
              _showEditAttributeModal(
                context,
                index,
                attributeData.name ?? '',
                attributeData.displayName ?? '',
                attributeData.dataType ?? '',
                (attributeData.required ?? false) ? 'Yes' : 'No',
                (attributeData.unique ?? false) ? 'Yes' : 'No',
              );
            },
            icon: const Icon(Icons.edit_outlined),
            color: Colors.blue[600],
            iconSize: 18,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            tooltip: 'Edit',
          ),
          const SizedBox(width: 4),
          IconButton(
            onPressed: () => _deleteAttribute(context, index, attributeName),
            icon: const Icon(Icons.delete_outline),
            color: Colors.red[600],
            iconSize: 18,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            tooltip: 'Delete',
          ),
        ],
      ),
    );
  }

  /// Builds unified table header that includes both data columns and actions column
  Widget _buildUnifiedTableHeader(
      BuildContext context, ObjectCreationModel? objectData) {
    final headers = _getDynamicAttributeHeaders(objectData);
    final widths = _getDynamicColumnWidths();

    return Row(
      children: [
        // Data column headers
        ...headers.map((header) {
          final width = widths[header] ?? 150.0;
          return SizedBox(
            width: width,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                header,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[700],
                ),
              ),
            ),
          );
        }).toList(),
        // Actions column header
        SizedBox(
          width: 100,
          child: Align(
            alignment: Alignment.center,
            child: Text(
              'ACTIONS',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.labelMedium(context),
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey[700],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Builds unified table row that includes both data and actions (no individual scrolling)
  Widget _buildUnifiedTableRow(
    BuildContext context,
    int index,
    Map<String, String> data,
    ObjectCreationModel? objectData,
  ) {
    final headers = _getDynamicAttributeHeaders(objectData);
    final widths = _getDynamicColumnWidths();
    final headerToKeyMap = _getHeaderToKeyMapping();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: [
          // Data columns (no individual scrolling)
          ...headers.map((header) {
            final width = widths[header] ?? 150.0;
            final key = headerToKeyMap[header] ?? header.toLowerCase();
            final value = data[key] ?? '';

            return SizedBox(
              width: width,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  value,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.labelMedium(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            );
          }).toList(),
          // Actions column
          SizedBox(
            width: 100,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  onPressed: () {
                    // Find the attribute data for editing
                    final attributeData = _attributeData[index];
                    _showEditAttributeModal(
                      context,
                      index,
                      attributeData.name ?? '',
                      attributeData.displayName ?? '',
                      attributeData.dataType ?? '',
                      (attributeData.required ?? false) ? 'Yes' : 'No',
                      (attributeData.unique ?? false) ? 'Yes' : 'No',
                    );
                  },
                  icon: const Icon(Icons.edit_outlined),
                  color: Colors.blue[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Edit',
                ),
                const SizedBox(width: 4),
                IconButton(
                  onPressed: () =>
                      _deleteAttribute(context, index, data['name'] ?? ''),
                  icon: const Icon(Icons.delete_outline),
                  color: Colors.red[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Delete',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds unified attribute row that includes both data columns and actions column
  Widget _buildUnifiedAttributeRow(
    BuildContext context,
    int index,
    Map<String, String> data,
    ObjectCreationModel? objectData,
  ) {
    final headers = _getDynamicAttributeHeaders(objectData);
    final widths = _getDynamicColumnWidths();
    final headerToKeyMap = _getHeaderToKeyMapping();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: [
          // Data columns
          ...headers.map((header) {
            final width = widths[header] ?? 150.0;
            final key = headerToKeyMap[header] ?? header.toLowerCase();
            final value = data[key] ?? '';

            return SizedBox(
              width: width,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  value,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.labelMedium(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            );
          }).toList(),
          // Actions column
          SizedBox(
            width: 100,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  onPressed: () {
                    // Find the attribute data for editing
                    final attributeData = _attributeData[index];
                    _showEditAttributeModal(
                      context,
                      index,
                      attributeData.name ?? '',
                      attributeData.displayName ?? '',
                      attributeData.dataType ?? '',
                      (attributeData.required ?? false) ? 'Yes' : 'No',
                      (attributeData.unique ?? false) ? 'Yes' : 'No',
                    );
                  },
                  icon: const Icon(Icons.edit_outlined),
                  color: Colors.blue[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Edit',
                ),
                const SizedBox(width: 4),
                IconButton(
                  onPressed: () =>
                      _deleteAttribute(context, index, data['name'] ?? ''),
                  icon: const Icon(Icons.delete_outline),
                  color: Colors.red[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Delete',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds unified attribute table row that includes both data and actions (no individual scrolling)
  Widget _buildUnifiedAttributeTableRow(
    BuildContext context,
    int index,
    Map<String, String> data,
    ObjectCreationModel? objectData,
  ) {
    final headers = _getDynamicAttributeHeaders(objectData);
    final widths = _getDynamicColumnWidths();
    final headerToKeyMap = _getHeaderToKeyMapping();

    return Container(
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: [
          // Data columns
          ...headers.map((header) {
            final width = widths[header] ?? 150.0;
            final key = headerToKeyMap[header] ?? header.toLowerCase();
            final value = data[key] ?? '';

            return Container(
              width: width,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Text(
                value,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            );
          }).toList(),
          // Actions column
          Container(
            width: 100,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  onPressed: () {
                    // Find the attribute data for editing
                    final attributeData = _attributeData[index];
                    _showEditAttributeModal(
                      context,
                      index,
                      attributeData.name ?? '',
                      attributeData.displayName ?? '',
                      attributeData.dataType ?? '',
                      (attributeData.required ?? false) ? 'Yes' : 'No',
                      (attributeData.unique ?? false) ? 'Yes' : 'No',
                    );
                  },
                  icon: const Icon(Icons.edit_outlined),
                  color: Colors.blue[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Edit',
                ),
                const SizedBox(width: 4),
                IconButton(
                  onPressed: () =>
                      _deleteAttribute(context, index, data['name'] ?? ''),
                  icon: const Icon(Icons.delete_outline),
                  color: Colors.red[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Delete',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds attribute table row with proper horizontal scrolling
  Widget _buildAttributeTableRow(
    BuildContext context,
    int index,
    Map<String, String> data,
    ObjectCreationModel? objectData,
  ) {
    final headers = _getDynamicAttributeHeaders(objectData);
    final widths = _getDynamicColumnWidths();
    final headerToKeyMap = _getHeaderToKeyMapping();

    return Container(
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: [
          // Data columns
          ...headers.map((header) {
            final width = widths[header] ?? 150.0;
            final key = headerToKeyMap[header] ?? header.toLowerCase();
            final value = data[key] ?? '';

            return Container(
              width: width,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Text(
                value,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            );
          }).toList(),
          // Actions column
          Container(
            width: 100,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: const BoxDecoration(
              border: Border(
                left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  onPressed: () {
                    // Find the attribute data for editing
                    final attributeData = _attributeData[index];
                    _showEditAttributeModal(
                      context,
                      index,
                      attributeData.name ?? '',
                      attributeData.displayName ?? '',
                      attributeData.dataType ?? '',
                      (attributeData.required ?? false) ? 'Yes' : 'No',
                      (attributeData.unique ?? false) ? 'Yes' : 'No',
                    );
                  },
                  icon: const Icon(Icons.edit_outlined),
                  color: Colors.blue[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Edit',
                ),
                const SizedBox(width: 4),
                IconButton(
                  onPressed: () =>
                      _deleteAttribute(context, index, data['name'] ?? ''),
                  icon: const Icon(Icons.delete_outline),
                  color: Colors.red[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Delete',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds scrollable attribute row for the new table layout
  Widget _buildScrollableAttributeRow(
    BuildContext context,
    int index,
    Map<String, String> data,
    ObjectCreationModel? objectData,
  ) {
    final headers = _getDynamicAttributeHeaders(objectData);
    final widths = _getDynamicColumnWidths();
    final headerToKeyMap = _getHeaderToKeyMapping();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: headers.map((header) {
          final width = widths[header] ?? 150.0;
          final key = headerToKeyMap[header] ?? header.toLowerCase();
          final value = data[key] ?? '';

          return Container(
            width: width,
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: Text(
              value,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.labelMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          );
        }).toList(),
      ),
    );
  }

  /// Builds scrollable attribute table row for the shared scroll controller implementation
  Widget _buildScrollableAttributeTableRow(
    BuildContext context,
    int index,
    Map<String, String> data,
    ObjectCreationModel? objectData,
  ) {
    final headers = _getDynamicAttributeHeaders(objectData);
    final widths = _getDynamicColumnWidths();
    final headerToKeyMap = _getHeaderToKeyMapping();

    return Container(
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: headers.map((header) {
          final width = widths[header] ?? 150.0;
          final key = headerToKeyMap[header] ?? header.toLowerCase();
          final value = data[key] ?? '';

          return Container(
            width: width,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Text(
              value,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.labelMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          );
        }).toList(),
      ),
    );
  }

  /// Builds fixed actions table row for the shared scroll controller implementation
  Widget _buildFixedActionsTableRow(
    BuildContext context,
    int index,
    Map<String, String> data,
  ) {
    return Container(
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: IgnorePointer(
          ignoring: !Provider.of<WebHomeProviderStatic>(context).isAIMode,
          child: Opacity(
            opacity: !Provider.of<WebHomeProviderStatic>(context).isAIMode
                ? 0.5
                : 1.0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  onPressed: () {
                    // Find the attribute data for editing
                    final attributeData = _attributeData[index];
                    _showEditAttributeModal(
                      context,
                      index,
                      attributeData.name ?? '',
                      attributeData.displayName ?? '',
                      attributeData.dataType ?? '',
                      (attributeData.required ?? false) ? 'Yes' : 'No',
                      (attributeData.unique ?? false) ? 'Yes' : 'No',
                    );
                  },
                  icon: const Icon(Icons.edit_outlined),
                  color: Colors.blue[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Edit',
                ),
                const SizedBox(width: 4),
                IconButton(
                  onPressed: () =>
                      _deleteAttribute(context, index, data['name'] ?? ''),
                  icon: const Icon(Icons.delete_outline),
                  color: Colors.red[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Delete',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Builds fixed actions row for the new table layout
  Widget _buildFixedAttributeActionsRow(
    BuildContext context,
    int index,
    ObjectAttribute data,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          IconButton(
            onPressed: () {
              // Find the attribute data for editing
              final attributeData = _attributeData[index];
              _showEditAttributeModal(
                context,
                index,
                attributeData.name ?? '',
                attributeData.displayName ?? '',
                attributeData.dataType ?? '',
                (attributeData.required ?? false) ? 'Yes' : 'No',
                (attributeData.unique ?? false) ? 'Yes' : 'No',
              );
            },
            icon: const Icon(Icons.edit_outlined),
            color: Colors.blue[600],
            iconSize: 18,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            tooltip: 'Edit',
          ),
          const SizedBox(width: 4),
          IconButton(
            onPressed: () => _deleteAttribute(context, index, data.name ?? ''),
            icon: const Icon(Icons.delete_outline),
            color: Colors.red[600],
            iconSize: 18,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            tooltip: 'Delete',
          ),
        ],
      ),
    );
  }
}

class AccordionItem {
  final String id;
  final String title;
  final String? subtitle;
  final List<String> children;

  AccordionItem({
    required this.id,
    required this.title,
    this.subtitle,
    required this.children,
  });
}

// Shared HoverBellIcon component that can be used across the application
class HoverBellIcon extends StatefulWidget {
  final VoidCallback onTap;

  const HoverBellIcon({
    super.key,
    required this.onTap,
  });

  @override
  State<HoverBellIcon> createState() => _HoverBellIconState();
}

class _HoverBellIconState extends State<HoverBellIcon> {
  bool isHovered = false;
  OverlayEntry? _overlayEntry;
  final GlobalKey _bellIconKey = GlobalKey();
  bool _isOverPopup = false;

  @override
  void dispose() {
    _removeOverlay();
    super.dispose();
  }

  void _showOverlay() {
    if (_overlayEntry != null) return;

    final RenderBox? renderBox =
        _bellIconKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final position = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: position.dx - 280, // Adjust horizontal position
        top: position.dy + size.height + 8, // Position below the bell icon
        child: MouseRegion(
          onEnter: (_) {
            _isOverPopup = true;
          },
          onExit: (_) {
            _isOverPopup = false;
            // Small delay to check if we're still hovering
            Future.delayed(Duration(milliseconds: 50), () {
              if (mounted && !isHovered && !_isOverPopup) {
                _removeOverlay();
              }
            });
          },
          child: Material(
            color: Colors.transparent,
            child: _buildHoverPopup(),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _handleMouseExit() {
    setState(() => isHovered = false);
    // Small delay to allow moving to popup
    Future.delayed(Duration(milliseconds: 50), () {
      if (mounted && !isHovered && !_isOverPopup) {
        _removeOverlay();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) {
        setState(() => isHovered = true);
        _showOverlay();
      },
      onExit: (_) {
        _handleMouseExit();
      },
      child: InkWell(
        onTap: widget.onTap,
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory,
        child: Container(
          child: Padding(
            padding: const EdgeInsets.all(4.0),
            child: Icon(
              key: _bellIconKey,
              Icons.notifications_outlined,
              size: 18,
              color: Color(0xffFF2019),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHoverPopup() {
    return Container(
      width: 300,
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Message text
          Text(
            'This Objects is already exists in your library. You need to rename the Objects to proceed.',
            textAlign: TextAlign.center,
            style: FontManager.getCustomStyle(
              fontSize: 14,
              color: Colors.black87,
              fontWeight: FontManager.regular,
              fontFamily: FontManager.fontFamilyTiemposText,
              height: 1.4,
            ),
          ),
          SizedBox(height: 20),
          // Buttons row
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Continue button (outlined)
              SizedBox(
                height: 32,
                child: OutlinedButton(
                  onPressed: () {
                    setState(() {
                      isHovered = false;
                      _isOverPopup = false;
                    });
                    _removeOverlay();
                    // Handle continue action
                  },
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: Colors.grey.shade400, width: 1),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    backgroundColor: Colors.white,
                  ),
                  child: Text(
                    'Continue',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      color: Colors.black87,
                      fontWeight: FontManager.medium,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 12),
              // Resolve button (filled)
              SizedBox(
                height: 32,
                child: ElevatedButton(
                  onPressed: () {
                    setState(() {
                      isHovered = false;
                      _isOverPopup = false;
                    });
                    _removeOverlay();
                    // Show the Object Creation Error dialog
                    _showObjectCreationErrorDialog(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(0xff007AFF),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    elevation: 0,
                  ),
                  child: Text(
                    'Resolve',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      color: Colors.white,
                      fontWeight: FontManager.medium,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showObjectCreationErrorDialog(BuildContext context) {
    final TextEditingController customerController =
        TextEditingController(text: 'Customer_1');

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            width: 534,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header section with shadow
                Container(
                  padding: const EdgeInsets.fromLTRB(24, 24, 24, 16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: Radius.circular(12),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Color(0x0F000000), // #0000000F
                        blurRadius: 12,
                        offset: Offset(0, 6),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Object Creation Error',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.titleLarge(context),
                          fontWeight: FontWeight.w600,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                        iconSize: 18,
                        color: Colors.grey[600],
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                ),

                // Content section
                Container(
                  padding: const EdgeInsets.fromLTRB(20, 16, 20, 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Purpose section
                      Text(
                        'Purpose',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.titleMedium(context),
                          fontWeight: FontWeight.w600,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                      const SizedBox(height: 6),
                      Text(
                        'This Objects is already exists in your library. You need to rename the Objects to proceed.',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.titleMedium(context),
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey[700],
                          height: 1.3,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Customer field label
                      Text(
                        'Customer_1',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.titleSmall(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                      const SizedBox(height: 6),

                      // Text input field
                      Container(
                        width: double.infinity,
                        child: TextFormField(
                          controller: customerController,
                          decoration: InputDecoration(
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(4),
                              borderSide: BorderSide(color: Colors.grey[300]!),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(4),
                              borderSide: BorderSide(color: Colors.grey[300]!),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(4),
                              borderSide:
                                  const BorderSide(color: Color(0xFF007AFF)),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 8),
                            filled: true,
                            fillColor: Colors.white,
                          ),
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.titleSmall(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Footer section with shadow
                Container(
                  padding: const EdgeInsets.fromLTRB(20, 16, 20, 20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(12),
                      bottomRight: Radius.circular(12),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color:
                            Color(0x0F000000), // #0000000F (black, 6% opacity)
                        blurRadius: 12,
                        offset: Offset(0, -6),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: ElevatedButton(
                      onPressed: () {
                        // Handle proceed action
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF007AFF),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4),
                        ),
                        elevation: 0,
                      ),
                      child: Text(
                        'Proceed',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.titleSmall(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
