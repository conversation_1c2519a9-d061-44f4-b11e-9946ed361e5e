// entity_text_formatter.dart
import 'package:nsl/models/object_creation_model.dart';

class EntityTextFormatter {
  const EntityTextFormatter._();

  // ───────────────────── Top-level entry point ──────────────────────
  static String toNarrative(ObjectCreationModel model) {
    final sb = StringBuffer();

    // 1. Tenant
    sb.writeln('Tenant: ${model.tenant ?? ''}');

    // 2. Dynamic Entity declaration
    sb.writeln(_buildEntityDeclaration(model));

    // 3. Core metadata
    sb.writeln(
        'Entity: ${model.name ?? ''} - Entity Name: ${model.name ?? ''} - '
        'Display Name: ${model.displayName ?? ''} - Type: ${model.type ?? ''} - '
        'Description: ${model.description ?? ''} - Business Domain: ${model.businessDomain ?? ''} - '
        'Category: ${model.category ?? ''} - Tags: ${model.tags?.join(", ") ?? ''} - '
        'Archival Strategy: ${model.archivalStrategy ?? ''} - Icon: ${model.icon ?? ''} - '
        'Colour Theme: ${model.colorTheme ?? ''}');

    _appendAttributes(sb, model);
    _appendRelationships(sb, model);
    _appendBusinessRules(sb, model);
    _appendUiProperties(sb, model);
    _appendSecurity(sb, model);
    _appendEnums(sb, model);
    _appendSystemPermissions(sb, model);
    _appendRolePermissions(sb, model);

    return sb.toString().trim();
  }

  // ───────────────── Private section helpers ────────────────────────

  static String _buildEntityDeclaration(ObjectCreationModel model) {
    // Fallback to legacy text if present
    if ((model.entityDeclaration ?? '').isNotEmpty) {
      final txt = model.entityDeclaration!;
      return txt.trimRight().endsWith('.') ? txt : '$txt.';
    }

    if (model.attributes?.isEmpty ?? true) return '';

    // Build “EntityName has attr1, attr2^PK, attr3* [required], …” list.
    final tokens = model.attributes!
        .map(_tokeniseAttribute)
        .where((t) => t.isNotEmpty)
        .join(', ');

    return '${model.displayName ?? model.name ?? 'Entity'} has $tokens.';
  }

  static String _tokeniseAttribute(ObjectAttribute a) {
    final buf = StringBuffer(a.name ?? '');

    if (a.isPrimaryKey) buf.write('^PK');
    if (a.isForeignKey) buf.write('^FK');

    if (a.required == true) buf.write('* [required]');
    if (a.unique == true && !a.isPrimaryKey) buf.write(' [unique]');

    // Default value / type hints
    if ((a.dataType ?? '').toLowerCase() == 'enum' &&
        (a.defaultValue ?? '').isNotEmpty) {
      buf.write('* (${a.defaultValue})');
    } else if ((a.defaultValue ?? '').isNotEmpty &&
        (a.defaultType ?? '').toLowerCase() == 'static_value') {
      buf.write(' [default: ${a.defaultValue}]');
    } else if ((a.defaultType ?? '').toLowerCase() == 'derived') {
      buf.write('[derived]');
    } else if ((a.defaultType ?? '').toLowerCase() == 'dependent') {
      buf.write('[dependent]');
    }

    // “information” tag for non-required, free-text fields
    if (!(a.required ?? false) &&
        !a.isPrimaryKey &&
        !a.isForeignKey &&
        (a.dataType ?? '').toLowerCase() == 'text') {
      buf.write(' [information]');
    }

    return buf.toString();
  }

  static void _appendAttributes(StringBuffer sb, ObjectCreationModel model) {
    if (model.attributes?.isEmpty ?? true) return;
    sb.writeln(
        'Attributes: Attribute Name | Display Name | Data Type | Required | Unique | '
        'Default Type | Default Value | Description | Helper Text');
    for (final a in model.attributes!) {
      sb.writeln('${a.name} | ${a.displayName ?? ''} | '
          '${a.dataType ?? ''} | ${a.required} | ${a.unique} | '
          '${a.defaultType ?? ''} | ${a.defaultValue ?? ''} | '
          '${a.description ?? ''} | ${a.helperText ?? ''}');
    }
  }

  // Remaining append* methods are unchanged from your last version
  static void _appendRelationships(StringBuffer sb, ObjectCreationModel m) {
    /* … */
  }
  static void _appendBusinessRules(StringBuffer sb, ObjectCreationModel m) {
    /* … */
  }
  static void _appendUiProperties(StringBuffer sb, ObjectCreationModel m) {
    /* … */
  }
  static void _appendSecurity(StringBuffer sb, ObjectCreationModel m) {/* … */}
  static void _appendEnums(StringBuffer sb, ObjectCreationModel m) {/* … */}
  static void _appendSystemPermissions(StringBuffer sb, ObjectCreationModel m) {
    /* … */
  }
  static void _appendRolePermissions(StringBuffer sb, ObjectCreationModel m) {
    /* … */
  }
}
